// Code generated by "dbusutil-gen em -type ImageEffect"; DO NOT EDIT.

package image_effect

import (
	"github.com/linuxdeepin/go-lib/dbusutil"
)

func (v *ImageEffect) GetExportedMethods() dbusutil.ExportedMethods {
	return dbusutil.ExportedMethods{
		{
			Name:   "Delete",
			Fn:     v.Delete,
			InArgs: []string{"effect", "filename"},
		},
		{
			Name:    "Get",
			Fn:      v.Get,
			InArgs:  []string{"effect", "filename"},
			OutArgs: []string{"outputFile"},
		},
	}
}
