// SPDX-FileCopyrightText: 2022 UnionTech Software Technology Co., Ltd.
//
// SPDX-License-Identifier: GPL-3.0-or-later

// Code generated by "dbusutil-gen em -type Config"; DO NOT EDIT.

package dsync

import (
	"github.com/linuxdeepin/go-lib/dbusutil"
)

func (v *Config) GetExportedMethods() dbusutil.ExportedMethods {
	return dbusutil.ExportedMethods{
		{
			Name:    "Get",
			Fn:      v.Get,
			OutArgs: []string{"data"},
		},
		{
			Name:   "Set",
			Fn:     v.Set,
			InArgs: []string{"data"},
		},
	}
}
