Source: dde-daemon
Section: admin
Priority: optional
Maintainer: Deepin Packages Builder <<EMAIL>>
Build-Depends:
 dde-api-dev (>> 3.17.1+),
 debhelper-compat (= 11),
 deepin-desktop-schemas,
 deepin-gettext-tools,
 golang-github-linuxdeepin-go-lib-dev(>> 5.1.0+),
 golang-gir-gio-2.0-dev (>> 1.2.0+),
 golang-gir-glib-2.0-dev (>> 1.2.0+),
 golang-gir-gobject-2.0-dev (>> 1.2.0+),
 golang-gir-gudev-1.0-dev (>> 1.2.0+),
 golang-github-axgle-mahonia-dev,
 golang-github-davecgh-go-spew-dev,
 golang-github-fsnotify-fsnotify-dev,
 golang-github-gosexy-gettext-dev,
 golang-github-jinzhu-gorm-dev,
 golang-github-kelvins-sunrisesunset-dev,
 golang-github-linuxdeepin-go-dbus-factory-dev (>= 1.4.0),
 golang-github-linuxdeepin-go-x11-client-dev (>= 0.0.4),
 golang-github-lofanmi-pinyin-dev,
 golang-github-mattn-go-sqlite3-dev,
 golang-github-mozillazg-go-pinyin-dev,
 golang-github-msteinert-pam-dev,
 golang-github-nfnt-resize-dev,
 golang-github-rickb777-date-dev,
 golang-github-smartystreets-goconvey-dev,
 golang-github-teambition-rrule-go-dev,
 golang-github-adrg-xdg-dev,
 golang-go,
 golang-golang-x-sys-dev,
 golang-golang-x-xerrors-dev,
 golang-gopkg-check.v1-dev,
 golang-gopkg-yaml.v3-dev,
 golang-google-protobuf-dev,
 golang-github-mdlayher-netlink-dev,
 golang-github-jouyouyun-hardware-dev,
 libfontconfig1-dev,
 libglib2.0-dev,
 libgtk-3-dev,
 libinput-dev,
 libnl-3-dev,
 libnl-genl-3-dev,
 librsvg2-bin,
 libudev-dev,
 libx11-dev,
 libxcb-record0-dev,
 libxcb1-dev,
 libxcursor-dev,
 libxfixes-dev,
 libxi-dev,
 libxkbfile-dev,
 libxtst-dev,
 libddcutil-dev,
 python3,
Standards-Version: 4.5.1
Homepage: http://www.deepin.org

Package: dde-daemon
Architecture: any
Depends:
 bamfdaemon,
 bluez-obexd,
 cgroup-tools,
 dbus,
 dde-api (>> 3.17.1+),
 dde-polkit-agent,
 deepin-desktop-schemas (>> 5.1.2+),
 deepin-installer-timezones,
 deepin-proxy,
 deepin-sound-theme,
 dmidecode,
 dnsmasq-base,
 gnome-keyring,
 libglib2.0-bin,
 hwinfo,
 imwheel,
 ipwatchd,
 iso-codes,
 lastore-daemon,
 libfprint0 | libfprint | libfprint-2-2,
 libnl-3-200,
 libnl-genl-3-200,
 libnotify-bin,
 libpam-runtime (>= 1.1.3-2~),
 libpam0g,
 libnss-myhostname,
 mobile-broadband-provider-info,
 network-manager,
 procps,
 rfkill,
 tlp,
 user-setup,
 xdotool,
 xkb-data,
 dde-dconfig-daemon,
 ${dist:Depends},
 ${misc:Depends},
 ${shlibs:Depends},
Breaks:
 lastore-daemon(<< 0.9.64),
Replaces:
 lastore-daemon(<< 0.9.64),
Conflicts:
 startdde,
 dde-workspace,
 lastore-daemon-migration,
Provides:
 lastore-daemon-migration,
Recommends:
 flatpak,
 iio-sensor-proxy,
 tlp,
 proxychains4,
 mesa-utils,
Suggests:
 bluez (>=5.4),
 miraclecast,
 network-manager-l2tp,
 network-manager-openconnect,
 network-manager-openvpn,
 network-manager-pptp,
 network-manager-sstp,
 network-manager-vpnc,
 xserver-xorg-input-synaptics,
 xserver-xorg-input-wacom,
Description: daemon handling the DDE session settings
 This package contains the daemon which is responsible for setting the
 various parameters of a DDE session and the applications that run
 under it.
