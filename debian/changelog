dde-daemon (6.1.45) unstable; urgency=medium

  * fix: add nil checks for audio sink/source updates
  * fix: The scheduled shutdown during holidays takes effect

 -- z<PERSON><PERSON>n <<EMAIL>>  <PERSON><PERSON>, 22 Jul 2025 11:26:59 +0800

dde-daemon (6.1.44) unstable; urgency=medium

  * fix: fix some modules loaded failed
  * fix: fix failure to change audio input device

 -- fuleyi <<EMAIL>>  Tu<PERSON>, 15 Jul 2025 20:50:41 +0800

dde-daemon (6.1.43) unstable; urgency=medium

  * i18n: Updates for project Deepin Desktop Environment (#823)
  * feat: add GenLocale method and improve locale state handling
  * fix: avoid dpkg packaging failures
  * feat: root进程需要判断调用者时，使用TrustedExe替换Exe
  * Revert "chore: prohibit the startup and auto start of some services"

 -- WuJiangYu <<EMAIL>>  Thu, 10 Jul 2025 20:58:57 +0800

dde-daemon (6.1.42) unstable; urgency=medium

  * fix: add tlp dependency to debian control file
  * fix: add nil check for xs<PERSON>anager in tryToChangeScaleFactor
  * chore: prohibit the startup and auto start of some services

 -- fuleyi <<EMAIL>>  Thu, 03 Jul 2025 17:22:54 +0800

dde-daemon (6.1.41) unstable; urgency=medium

  * fix: remove redundant own policy in Accounts1 config
  * fix: Fix incompatible function pointer types with g_timeout_add_full
    on GCC 15

 -- YeShanShan <<EMAIL>>  Fri, 27 Jun 2025 15:47:51 +0800

dde-daemon (6.1.40) unstable; urgency=medium

  * chore: remove regional variants for es languages
  * i18n: Updates for project Deepin Desktop Environment (#813)

 -- WuJiangYu <<EMAIL>>  Mon, 23 Jun 2025 17:16:10 +0800

dde-daemon (6.1.39) unstable; urgency=medium

  * fix: reorder module dependencies in session daemon and update
    imports

 -- fuleyi <<EMAIL>>  Sat, 21 Jun 2025 15:35:24 +0800

dde-daemon (6.1.38) unstable; urgency=medium

  * fix: update text scale factor in XSettings configuration

 -- fuleyi <<EMAIL>>  Fri, 20 Jun 2025 18:26:08 +0800

dde-daemon (6.1.37) unstable; urgency=medium

  * fix: Add interface to obtain all keyboard layouts
  * i18n: Updates for project Deepin Desktop Environment (#807)

 -- caixiangrong <<EMAIL>>  Thu, 19 Jun 2025 16:38:27 +0800

dde-daemon (6.1.36) unstable; urgency=medium

  * fix: Modify translations to align with Control Center
  * fix: adjust gesture feature translations and reorder options
  * fix: Modify the refresh rate accuracy to 0.01
  * i18n: Updates for project Deepin Desktop Environment (#774)

 -- fuleyi <<EMAIL>>  Thu, 12 Jun 2025 20:50:06 +0800

dde-daemon (6.1.35) unstable; urgency=medium

  * feat: add lightdm-deepin-greeter configuration files to installation
    for quick login
  * refactor: remove ai-assistant shortcuts and related actions
  * fix: 修复换不同显示器后系统缩放未改变的问题。
  * feat: integrate keyboard management with dsg configuration
  * fix: remove manual mode check and adjust auto mode logic
  * feat: 快捷键切换屏幕时判断有效屏幕数量

 -- zhangkun <<EMAIL>>  Thu, 05 Jun 2025 20:48:02 +0800

dde-daemon (6.1.34) unstable; urgency=medium

  * chore: 更正一些dbus的调用，使用org风格接口名进行调用
  * fix: 解决降噪设置被重复触发的问题

 -- fuleyi <<EMAIL>>  Thu, 29 May 2025 15:36:14 +0800

dde-daemon (6.1.33) unstable; urgency=medium

  * fix: 修复认证漏洞
  * fix: extend custom timezone list
  * fix: 解决亮度概率异常的问题

 -- fuleyi <<EMAIL>>  Tue, 27 May 2025 20:26:35 +0800

dde-daemon (6.1.32) unstable; urgency=medium

  * feat: add fallback NTP server support

 -- YeShanShan <<EMAIL>>  Tue, 13 May 2025 21:20:26 +0800

dde-daemon (6.1.31) unstable; urgency=medium

  * fix: 修改噪音抑制脚本

 -- fuleyi <<EMAIL>>  Tue, 13 May 2025 19:37:31 +0800

dde-daemon (6.1.30) unstable; urgency=medium

  * fix: 解决蓝牙文件传输失败的问题

 -- fuleyi <<EMAIL>>  Thu, 08 May 2025 17:11:02 +0800

dde-daemon (6.1.29) unstable; urgency=medium

  * chore: add transifex config file
  * fix: starting with none screen, then inserted a screen, the screen
    cannot be lit
  * feat: Provide osbuild properties
  * fix: 解决dde-session-daemon启动慢的问题

 -- YeShanShan <<EMAIL>>  Tue, 29 Apr 2025 10:56:39 +0800

dde-daemon (6.1.28) unstable; urgency=medium

  * feat: change default lock wallpaper path

 -- zhangkun <<EMAIL>>  Tue, 22 Apr 2025 20:32:29 +0800

dde-daemon (6.1.27) unstable; urgency=medium

  * fix: 解决屏保没有同步的问题
  * fix: 解决kwin升级后导致快捷键的问题

 -- fuleyi <<EMAIL>>  Thu, 17 Apr 2025 20:48:57 +0800

dde-daemon (6.1.26) unstable; urgency=medium

  * chore: display & xsettings moved to dde-daemon

 -- fuleyi <<EMAIL>>  Wed, 02 Apr 2025 14:26:56 +0800

dde-daemon (6.1.25) unstable; urgency=medium

  * fix: 用户添加删除壁纸，发送信号
  * fix: 热键使用dde-am启动应用，兼容玲珑应用

 -- fuleyi <<EMAIL>>  Thu, 27 Mar 2025 14:43:33 +0800

dde-daemon (6.1.24) unstable; urgency=medium

  * fix: 解决控制中心设置启动动画尺寸后，重启无法进入系统的问题
  * fix: 磁盘不足的通知，前往清理的文案没有翻译 

 -- fuleyi <<EMAIL>>  Thu, 13 Mar 2025 17:07:00 +0800

dde-daemon (6.1.23) unstable; urgency=medium

  * feat: custom wallpaper maximum 20

 -- fuleyi <<EMAIL>>  Wed, 05 Mar 2025 17:35:43 +0800

dde-daemon (6.1.22) unstable; urgency=medium

  * fix: 解决磐石系统下uadp存储的问题

 -- Chengqi E <<EMAIL>>  Thu, 27 Feb 2025 16:04:06 +0800

dde-daemon (6.1.21) unstable; urgency=medium

  * fix: 解决磐石系统下设置不了壁纸的问题
  * chore: 解决壁纸没有保存的问题

 -- fuleyi <<EMAIL>>  Thu, 20 Feb 2025 17:34:06 +0800

dde-daemon (6.1.20) unstable; urgency=medium

  * fix: 修复台式机电源管理中使用电源界面,显示有笔记本合盖时设置项问题
  * fix: Accept bluetooth file notification will never timeout (#743)
  * fix: 修复低电量阈值下拉框显示空白问题
  * fix: 修复电源管理模块崩溃问题
  * fix: 修复手势翻译错误的问题

 -- fuleyi <<EMAIL>>  Thu, 13 Feb 2025 15:28:30 +0800

dde-daemon (6.1.19) unstable; urgency=medium

  * fix: 解决切换grub启动菜单失败的问题

 -- fuleyi <<EMAIL>>  Fri, 24 Jan 2025 09:59:48 +0800

dde-daemon (6.1.18) unstable; urgency=medium

  * fix: 设置手势的动作后，发送infos变化的信号

 -- fuleyi <<EMAIL>>  Thu, 16 Jan 2025 17:38:52 +0800

dde-daemon (6.1.17) unstable; urgency=medium

  * feat: 设置三指短触手势为打开关闭全局搜索
  * fix: 添加窗口铺至左右侧快捷键的翻译，更新go.mod
  * fix: 优化触控板逻辑
  * fix: 手势动作支持设置

 -- fuleyi <<EMAIL>>  Thu, 09 Jan 2025 11:18:00 +0800

dde-daemon (6.1.16) unstable; urgency=medium

  * fix: 适配libinput的升级，解决收不到tap事件的问题
  * fix: 解决ImageEffect1接口调用失败的问题
  * fix: 由于权限管控，预先创建/var/cache/deepin/dde-daemon目录

 -- fuleyi <<EMAIL>>  Fri, 03 Jan 2025 11:03:48 +0800

dde-daemon (6.1.15) unstable; urgency=medium

  * fix: 适配pipewire，解决音频设备模式切换失败的问题

 -- fuleyi <<EMAIL>>  Tue, 24 Dec 2024 11:43:20 +0800

dde-daemon (6.1.14) unstable; urgency=medium

  * fix: 解决禁用触控板无法禁用的问题
  * fix: 修复按电源键“关闭显示器”无法再次按电源键点亮显示器问题

 -- fuleyi <<EMAIL>>  Thu, 19 Dec 2024 11:10:23 +0800

dde-daemon (6.1.13) unstable; urgency=medium

  * fix: 定时关闭倒计时通知取消关闭按钮
  * feat: 音频单声道设置取消pipewire限制
  * feat: 音频框架默认pipewire，且treeland环境强制切换为pipewire框架
  * feat: 设置默认主题为nirvana
  * chore: update translations

 -- fuleyi <<EMAIL>>  Fri, 13 Dec 2024 14:47:41 +0800

dde-daemon (6.1.12) unstable; urgency=medium

  * fix: 解决dconfig属性类型不对导致崩溃的问题
  * fix: 修复节能设置中低电量时自动开启节能模式和使用电池时自动开启节能模式默认值与需求不符问题
  * fix: 解决安全加固导致image-blur创建不了文件的问题
  * fix: treeland下可以导出StatusNotifierWatcher接口

 -- fuleyi <<EMAIL>>  Fri, 06 Dec 2024 17:17:38 +0800

dde-daemon (6.1.11) unstable; urgency=medium

  * feat: 修改语音朗读快捷键动作
  * feat: treeland环境下音频框架默认使用pipewire

 -- fuleyi <<EMAIL>>  Fri, 29 Nov 2024 11:34:39 +0800

dde-daemon (6.1.10) unstable; urgency=medium

  * feat: 修复安全漏洞

 -- fuleyi <<EMAIL>>  Fri, 22 Nov 2024 10:59:32 +0800

dde-daemon (6.1.9) unstable; urgency=medium

  * feat: automatic switch power saving mode threshold
  * fix: 创建配置目录: /var/lib/dde-daemon
  * fix: 解决power的dconfig配置初始化失败的问题
  * fix: 解决热键不响应问题
  * chore: 更正bluez属性Percentage的类型
  * feat: support selecting the action when the battery is low
  * fix: wrong bus name of dde-lock-service

 -- fuleyi <<EMAIL>>  Wed, 20 Nov 2024 14:21:58 +0800

dde-daemon (6.1.8) unstable; urgency=medium

  * fix: 修复用户体验计划不可用问题
  * chore: 修改遗漏的dbus接口变更
  * chore: 解决super+lock不锁屏的问题
  * chore: 添加时区，并且将时间的配置改为dconfig控制

 -- fuleyi <<EMAIL>>  Thu, 07 Nov 2024 13:49:01 +0800

dde-daemon (6.1.7) unstable; urgency=medium

  * chore: 修改网络代理dbus接口名称

  -- caixiangrong <<EMAIL>>  Thu, 24 Oct 2024 17:15:40 +0800

dde-daemon (6.1.6) unstable; urgency=medium

  * feat: 解决dde-system-daemon服务启动不了的问题 (#663)

 -- fuleyi <<EMAIL>>  Tue, 22 Oct 2024 17:03:02 +0800

dde-daemon (6.1.5) unstable; urgency=medium

  * feat: 解决dde-system-daemon服务启动不了的问题 (#663)

 -- fuleyi <<EMAIL>>  Tue, 22 Oct 2024 13:20:09 +0800

dde-daemon (6.1.4) unstable; urgency=medium

  * feat: 增加deepin-daemon和deepin-admin-daemon两个用户

 -- fuleyi <<EMAIL>>  Mon, 21 Oct 2024 19:14:24 +0800

dde-daemon (6.1.3) unstable; urgency=medium

  * 添加dde-session-daemon 的service文件

 -- fuleyi <<EMAIL>>  Fri, 18 Oct 2024 19:08:32 +0800

dde-daemon (6.1.2) unstable; urgency=medium

  * chore:修复dde-daemon编译问题
  * chore: remove deprecated ioutil
  * chore: systemd hardening
  * chore: systemd文件调整，makefile去掉冗余的安装 (linuxdeepin#654)

 -- fuleyi <<EMAIL>>  Fri, 18 Oct 2024 14:34:13 +0800

dde-daemon (6.1.1) unstable; urgency=medium

  * chore: cherry-pick paths from professional

 -- fuleyi <<EMAIL>>  Mon, 02 Sep 2024 15:18:43 +0800

dde-daemon (6.1.0) unstable; urgency=medium

  * 1070代码迁移，接口调整为v23风格

 -- fuleyi <<EMAIL>>  Tue, 16 Jul 2024 09:47:14 +0800

dde-daemon (5.15.13.1) unstable; urgency=medium

  [ Deepin Packages Builder ]
  * fix: 监听飞行模式和WIFI状态改变时使用定时显示OSD(Bug: 189383)(Influence: 显示飞行模式OSD时不显示WIFI连接OSD)
  * chore: 增加lastore日志(Influence: 日志)
  * fix: 修复播放器无法暂停导致音频服务阻塞问题(Bug: 198489)(Influence: 声音)
  * fix: dconfig类型默认为float64，直接转化成uint32会导致崩溃(Influence: dde-session-daemon崩溃)

 -- Deepin Packages Builder <<EMAIL>>  Tue, 13 Jun 2023 10:57:33 +0800

dde-daemon (5.15.12.3) unstable; urgency=medium

  [ Deepin Packages Builder ]
  * fix: 修复战斗版镜像待机/休眠后无法通过电源键唤醒的问题(Bug: 196477)(Influence: 待机/休眠功能)
  * fix: 修复待机唤醒后黑屏问题(Bug: 196477)(Influence: 修复待机唤醒后黑屏问题)
  * fix: 修复平衡模式错误问题(Bug: 199681)(Influence: 无)
  * chore: 更新翻译(Task: 267299)(Influence: 翻译)
  * fix: 第一次进入登陆界面界面缩放异常(Bug: 202729)(Influence: 升级之后缩放)

 -- Deepin Packages Builder <<EMAIL>>  Fri, 02 Jun 2023 17:07:13 +0800

dde-daemon (5.15.12.2) unstable; urgency=medium

  [ Deepin Packages Builder ]
  * fix: 默认开启aspm(Bug: 197329)(Influence: aspm)
  * feat: uosid等无原密码去修改密码的方式，keyring白盒加密不删除login.keyring(Task: 262399)(Influence: keyring白盒功能)

 -- Deepin Packages Builder <<EMAIL>>  Fri, 26 May 2023 17:11:02 +0800

dde-daemon (5.15.12.1) unstable; urgency=medium

  [ Deepin Packages Builder ]
  * fix: 处理飞行模式硬按键事件(KEY_RFKILL)(Bug: 196693)(Influence: 飞行模式硬开关)
  * fix: 修改存在pstate时,是否支持节能模式的判断(Bug: 200513)(Influence: 性能模式)
  * fix: grub2 获取os num的忽略标题修改(Task: 267459)(Influence: grub get os num)
  * feat: 扩展org.kde.StatusNotifierWatcher服务，允许托盘获取自身位置(Task: 266755)(Influence: 暂无)
  * feat: 修改应用管控通知(Task: 268523)(Influence: 应用管控通知)
  * feat: 新keyring白盒方案解耦dde-daemon，删除相关代码(Task: 262399)(Influence: keyring白盒功能)
  * feat: 修改应用管控通知内容(Task: 268523)(Influence: 应用管控通知)
  * chore(power): 性能模式优化，默认开启aspm(Task: 254685)(Influence: 性能模式)
  * chore: 更新翻译(Task: 267299)(Influence: 翻译)

 -- Deepin Packages Builder <<EMAIL>>  Thu, 25 May 2023 15:06:33 +0800

dde-daemon (5.15.11.1) unstable; urgency=medium

  [ Deepin Packages Builder ]
  * fix: 修复性能模式切换dbus属性不发出改变信号的问题(Task: 251417)(Influence: 可能影响性能模式切换导致的亮度不变的问题)
  * fix: 增加越界和空指针的判断(Influence: 进程崩溃)

 -- Deepin Packages Builder <<EMAIL>>  Thu, 11 May 2023 14:47:06 +0800

dde-daemon (5.15.11) unstable; urgency=medium

  [ lichangze ]
  * bump version to 5.15.4

  [ Deepin Packages Builder ]
  * fix: amdgpu balance use auto(Influence: amdgpu)
  * perf: 优化eventlog启动过程中的资源占用(Influence: 性能优化)
  * feat: 个性化提供界面模式设置(Task: 212923)(Influence: 个性化提供界面模式设置)
  * fix: 按照配置隐藏entry的驻留项(Bug: 180677)(Influence: dock应用驻留)
  * fix: wayland下关闭触控屏长按手势后，不响应菜单(Task: 21917)(Influence: 长按手势)
  * feat: 优化蓝牙接收文件后查看的选中效果(Bug: 183467)(Influence: 文件选中效果)
  * fix: 完善grub2的权限检查(Task: 220827)(Influence: grub2接口调用权限管控)
  * fix: 规避fonts-gb-st-super字体不显示问题(Bug: 182839)(Influence: 字体过滤条件)
  * fix: 修改DMIInfo数据类型(Bug: 183185)(Influence: DMIInfo数据类型)
  * feat: 支持ldap域用户登录(Task: 235581)(Influence: 域用户登录)
  * fix: 控制中心删除LDAP账户异常(Task: 235581)(Influence: 控制中心删除LDAP账户)
  * fix: 控制中心新建用户LDAP账户消失(Bug: 183531)(Influence: 控制中心新建用户)
  * fix: 控制中心删除域账户异常(Bug: 183747)(Influence: 控制中心删除账户)
  * fix: 控制中心LDAP域账户显示异常(Bug: 183741)(Influence: 控制中心LDAP域账户显示)
  * fix: 控制中心创建与AD域账户同名的账户异常(Bug: 184611)(Influence: 控制账户新建账户)
  * feat: 增加用户组增改删接口(Influence: 用户组接口)
  * feat: 增加对group修改删除的管控(Task: 237371)(Influence: group修改删除)
  * fix: 修改密码过期天数计算方法(Bug: 185533, 180171)
  * fix: 修复部分机器监控不到开合盖信号问题(Bug: 170577)(Influence: 监听开合盖信号)
  * chore: optimize imports(Influence: 优化导入)
  * fix: 修复传输文件名重复时查看选中的文件错误问题(Bug: 185919)(Influence: 蓝牙文件传输查看)
  * fix: 删除忘记密码重置密码清空keyring数据(Task: 241129)(Influence: 重置密码不情况密钥环)
  * feat: 802-1x 可以修改 identity 字段(Task: 212907)(Influence: 网络)
  * feat: 增加wpa-eap-suite-b-192(Task: 212881)(Influence: wifi)
  * feat: lastore模块逻辑修改(Task: 214983)(Influence: 通知和apt代理获取)
  * feat: 新增greeter壁纸管控(Task: 244565)(Influence: greeter壁纸管控)
  * feat: 1060埋点需求新增接口(Task: 212925)(Influence: 无)
  * fix: 修复jenkins单元测试报错(Influence: 构建)
  * fix: 新用户进入桌面时很卡问题修复(Bug: 187571)(Influence: 新用户进入桌面)
  * feat: 增加更新模式开关(Bug: 188459, 188481)(Influence: 更新模式)
  * fix: 解决弹出重置密码框后任务栏图标异常问题(Bug: 188605)(Influence: 打开重置密码框任务栏状态)
  * fix: 密码过期当天24时后，依然保持当天过期的提示和状态(Bug: 187343)
  * chore: 调出剪切板时隐藏启动器(Task: 246121)(Influence: 焦点变化)
  * fix: 提供自动回连配置(Bug: 187445)(Influence: 无)
  * fix: 点击一个无法被删除的用户组的删除图标后，点击添加用户组按钮没有响应(Bug: 188753)(Influence: 账户模块)
  * fix: 完善grub2的权限检查(Task: 220827)(Influence: grub2接口调用权限管控)
  * 已调研GNOME、KDE、麒麟等桌面环境，均未对时区做对应处理(Bug: 187343)
  * feat: login1重启后,重新inhibit(Task: 242863)(Influence: inhibit)
  * feat: 适配玲珑应用(Task: 229687)(Influence: 适配玲珑应用)
  * feat: 新增tty管控接口(Task: 242863)(Influence: tty管控)
  * fix: 拔掉电源时电池，延迟更新剩余时间计算(Bug: 100570)(Influence: 电池状态)
  * feat: 修改更新通知逻辑和触发时机(Task: 248045)(Influence: 更新通知)
  * fix: 修改loader异常处理(Influence: loader)
  * fix: 修复dock空指针问题(Influence: dock空指针)
  * fix: 修复蓝牙文件传输255字符失败问题(Bug: 191097)(Influence: 蓝牙文件传输)
  * fix: 玲珑应用后缀默认改为不显示(Bug: 190963)(Influence: 玲珑应用后缀)
  * fix: 修复按两次物理电源键，进入锁屏界面，按物理电源键，没有响应的问题(Bug: 191375)(Influence: 连续按两次电源键后锁屏)
  * fix: 完善代码，增加判空处理(Task: 252005)(Influence: 代码完善)
  * fix: 只要收到了systemd-login1的唤醒信号，就认为当前处于开盖状态(Bug: 191235)(Influence: 合盖休眠)
  * chore: 优化launcher模块启动时机(Task: 251417)(Influence: 桌面登录速度)
  * perf: 提供获取硬件数据的接口(Task: 251417)(Influence: com.deepin.system.SystemInfo系统服务下 GetLshwInfo 接口功能正常)
  * perf: 修正lshw的使用方法(Task: 251417)(Influence: systemInfo导出数据)
  * feat: XF86WebCam支持开/关相机(Bug: 192009)(Influence: 开关相机)
  * fix: DPMS on多次导致闪屏，改为300ms内触发多次不响应(Bug: 188981)(Influence: DPMS on多次)
  * feat(power): PANGU M900芯片性能模式适配(Task: 245915)(Influence: 电源-性能模式)
  * fix: 开启‘插入鼠标时禁用触控板’, 切换用户后, 拔掉鼠标, 触控板不可用(Bug: 193683)(Influence: 获取鼠标信息)
  * feat(systeminfo): 增加 CPUHardware 属性(Task: 248027)(Influence: 无)
  * feat: 新增安装错误提示通知(Task: 251749)(Influence: 更新提示通知)
  * fix: 增加是否允许显示WIFI连接断开OSD提示的配置(Bug: 189383)(Influence: 进入或退出飞行模式时不显示WIFI连接断开的OSD提示)
  * chore: 翻译通知文案(Task: 251749)(Influence: 翻译)
  * fix: 备份service使用错误(Task: 260263)(Influence: 通知)
  * fix: 正常系统更新不处理通知(Task: 260263)(Influence: 通知)
  * fix: 通知service使用错误(Task: 260263)(Influence: 通知)
  * fix: 修复几个用户数据没清除导致keyring无法解锁从而弹窗的问题(Task: 260271)(Influence: keyring弹窗)
  * ref: 使用override的方式规避规避fonts-gb-st-super字体不显示问题(Bug: 182839)(Influence: 字体过滤条件)
  * fix(bluetooth): 移除飞行模式蓝牙状态控制代码(Bug: 189125)(Influence: 飞行模式-蓝牙开关)
  * fix(systeminfo): 显示频率的值不正确(Bug: 191563)(Influence: 显示频率)
  * feat(display): 增加自动背光功能(Task: 250099)(Influence: 亮度)
  * fix: deepin-reader.desktop不支持image/tif删除SupportedType(Task: 255729)(Influence: 打开tif格式图片)
  * fix: add image/tif到deepin-image-viewer.desktop(Task: 255729)(Influence: 打开tif格式图片)
  * fix: 社区版调用账户IsUsernameValid崩溃(Bug: 189383)(Influence: 社区版本执行该接口)
  * feat: 系统不存在tpm设备时，不启动tpm服务(Task: 257009)(Influence: 按照操作步骤，当系统不存在tpm设备时，服务不启动，否则，服务启动)
  * feat(audio): 增加ReduceNoise默认值可配置(Task: 256189)(Influence: ReduceNoise 初值可配置)
  * fix: S3前关闭特效(Bug: 188981, 187917)(Influence: 闪屏)
  * fix: 修复icon误删问题(Bug: 197937)(Influence: 修改用户头像)
  * fix: 修复icon无法选中问题(Bug: 197947)(Influence: 修改用户头像)
  * fix: 打开播放器后dde-session-daemon崩溃(Bug: 197329)(Influence: 低电量播放视频导致崩溃)
  * fix: 修复按快捷键win+d ，启动器窗口不隐藏的问题(Bug: 198129)(Influence: 显示桌面快捷键)
  * feat(power): 性能模式优化(Task: 254685)(Influence: 性能模式)
  * feat: 增加系统默认高性能启动(Influence: 性能模式)
  * fix: 避免开机后高性能模式恢复后带来的亮度变化(Task: 251417)(Influence: 性能模式)

 -- Deepin Packages Builder <<EMAIL>>  Sat, 06 May 2023 13:57:32 +0800

dde-daemon (5.15.3) unstable; urgency=low

  * bump version to 5.15.3

 -- lichangze <<EMAIL>>  Mon, 9 Jan 2023 16:20:17 +0000

dde-daemon (3.0.18) unstable; urgency=low

  * Autobuild Tag 3.0.18 

 -- TagBuilder <<EMAIL>>  Fri, 12 Aug 2016 18:46:04 +0000

dde-daemon (3.0.17) unstable; urgency=low

  * Autobuild Tag 3.0.17 

 -- TagBuilder <<EMAIL>>  Fri, 22 Jul 2016 07:46:25 +0000

dde-daemon (3.0.16) unstable; urgency=low

  * Autobuild Tag 3.0.16 

 -- TagBuilder <<EMAIL>>  Mon, 30 May 2016 11:50:22 +0000

dde-daemon (3.0.15) unstable; urgency=low

  * Autobuild Tag 3.0.15 

 -- TagBuilder <<EMAIL>>  Fri, 27 May 2016 10:02:01 +0000

dde-daemon (3.0.14) unstable; urgency=low

  * Autobuild Tag 3.0.14 

 -- TagBuilder <<EMAIL>>  Thu, 26 May 2016 09:11:00 +0000

dde-daemon (3.0.13) unstable; urgency=low

  * Autobuild Tag 3.0.13 

 -- TagBuilder <<EMAIL>>  Mon, 23 May 2016 11:38:25 +0000

dde-daemon (3.0.11) unstable; urgency=low

  * Autobuild Tag 3.0.11 

 -- TagBuilder <<EMAIL>>  Wed, 24 Feb 2016 16:58:42 +0000

dde-daemon (3.0.10) unstable; urgency=low

  * Autobuild Tag 3.0.10 

 -- TagBuilder <<EMAIL>>  Mon, 22 Feb 2016 16:38:06 +0000

dde-daemon (3.0.7) stable; urgency=low

  * Autobuild Tag 3.0.7 

 -- TagBuilder <<EMAIL>>  Wed, 27 Jan 2016 21:40:25 +0000

dde-daemon (3.0.6) stable; urgency=low

  * Autobuild Tag 3.0.6 

 -- TagBuilder <<EMAIL>>  Fri, 22 Jan 2016 19:33:15 +0000

dde-daemon (3.0.5) stable; urgency=low

  * Autobuild Tag 3.0.5 

 -- TagBuilder <<EMAIL>>  Wed, 20 Jan 2016 20:07:25 +0000

dde-daemon (3.0.3) stable; urgency=low

  * Autobuild Tag 3.0.3 

 -- TagBuilder <<EMAIL>>  Tue, 12 Jan 2016 18:01:29 +0000

dde-daemon (3.0.2) stable; urgency=low

  * Autobuild Tag 3.0.2 

 -- TagBuilder <<EMAIL>>  Wed, 30 Dec 2015 20:48:37 +0000

dde-daemon (3.0.1) stable; urgency=low

  * Autobuild Tag 3.0.1 

 -- TagBuilder <<EMAIL>>  Tue, 29 Dec 2015 21:19:13 +0000

dde-daemon (3.0.0) stable; urgency=low

  * Autobuild Tag 3.0.0 

 -- TagBuilder <<EMAIL>>  Mon, 28 Dec 2015 20:41:11 +0000

dde-daemon (2.93.5) stable; urgency=low

  * Autobuild Tag 2.93.5 

 -- TagBuilder <<EMAIL>>  Sat, 19 Dec 2015 13:06:54 +0000

dde-daemon (2.93.4) stable; urgency=low

  * Autobuild Tag 2.93.4 

 -- TagBuilder <<EMAIL>>  Thu, 10 Dec 2015 20:29:24 +0000

dde-daemon (2.93.3) stable; urgency=low

  * Autobuild Tag 2.93.3 

 -- TagBuilder <<EMAIL>>  Thu, 10 Dec 2015 10:49:07 +0000

dde-daemon (2.93.2) stable; urgency=low

  * Autobuild Tag 2.93.2 

 -- TagBuilder <<EMAIL>>  Wed, 09 Dec 2015 21:54:19 +0000

dde-daemon (2.93.1) stable; urgency=low

  * Autobuild Tag 2.93.1 

 -- TagBuilder <<EMAIL>>  Fri, 20 Nov 2015 10:04:52 +0000

dde-daemon (2.93.0) stable; urgency=low

  * Autobuild Tag 2.93.0 

 -- TagBuilder <<EMAIL>>  Fri, 20 Nov 2015 09:22:43 +0000

dde-daemon (2.92.2) stable; urgency=low

  * Autobuild Tag 2.92.2 

 -- TagBuilder <<EMAIL>>  Thu, 06 Aug 2015 10:11:24 +0000

dde-daemon (2.92.1) stable; urgency=low

  * Autobuild Tag 2.92.1 

 -- TagBuilder <<EMAIL>>  Thu, 06 Aug 2015 09:55:44 +0000

dde-daemon (2.92.1) stable; urgency=low

  * Autobuild Tag 2.92.1 

 -- TagBuilder <<EMAIL>>  Fri, 24 Jul 2015 08:27:28 +0000

dde-daemon (2.91.0) stable; urgency=low

  * Autobuild Tag 2.91.0 

 -- TagBuilder <<EMAIL>>  Thu, 02 Jul 2015 16:25:32 +0000
