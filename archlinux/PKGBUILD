# <AUTHOR> <EMAIL>
pkgname=deepin-daemon-git
pkgver=6.0.0.r85.gf8733f93
pkgrel=1
pkgdesc='Daemon handling the DDE session settings'
arch=('x86_64' 'aarch64')
url="https://github.com/linuxdeepin/dde-daemon"
license=('GPL3')
depends=('deepin-desktop-schemas-git' 'ddcutil' 'deepin-api-git' 'gvfs' 'iso-codes' 'lsb-release'
         'mobile-broadband-provider-info' 'deepin-polkit-agent-git'
         'deepin-polkit-agent-ext-gnomekeyring-git' 'udisks2' 'upower'
         'libxkbfile' 'accountsservice' 'deepin-desktop-base-git' 'bamf' 'pulseaudio'
         'org.freedesktop.secrets' 'noto-fonts' 'imwheel' 'ddcutil')
makedepends=('golang-github-linuxdeepin-go-dbus-factory-git' 'golang-deepin-gir-git' 'golang-deepin-lib-git'
             'deepin-api-git' 'golang-github-nfnt-resize' 'sqlite' 'deepin-gettext-tools-git'
             'git' 'mercurial' 'python-gobject' 'networkmanager' 'bluez' 'go' 'ddcutil')
optdepends=('networkmanager: for network management support'
            'bluez: for bluetooth support'
            'iw: for miracast module'
            'proxychains-ng: for proxy configuration module')
conflicts=('deepin-daemon')
provides=('deepin-daemon')
groups=('deepin-git')
install="$pkgname.install"
source=("source.tar.gz"
        dde-daemon.patch
        remove-tc.patch
        'deepin-daemon.sysusers')
sha512sums=('SKIP'
            'SKIP'
            'SKIP'
            '808c02d4fec4cbbb01119bbb10499090199e738b7dd72c28a57dde098eef6132723f3434c151f79e21d9f788c7f7bae8046573ac93ba917afe0e803fbffa6d5a')

prepare() {
 cd $deepin_source_name
  patch -p1 -i ../remove-tc.patch
  patch -p1 -i ../dde-daemon.patch
  rm -rf system/uadp
  rm -rf session/uadpagent
  export GOPATH="$srcdir/build:/usr/share/gocode"
  export GO111MODULE=off
  # golang-deepin-lib's dependency, remove when go packaging resumes
  go get -v github.com/cryptix/wav
  go get -v github.com/axgle/mahonia
  go get -v github.com/msteinert/pam
  go get -v github.com/gosexy/gettext
  go get -v github.com/rickb777/date
  go get -v github.com/jinzhu/gorm
  go get -v github.com/kelvins/sunrisesunset
  go get -v github.com/mozillazg/go-pinyin
  go get -v github.com/teambition/rrule-go
  go get -v golang.org/x/xerrors
  go get -v github.com/mattn/go-sqlite3
  go get -v github.com/fsnotify/fsnotify
  go get -v github.com/godbus/dbus
  go get -v github.com/godbus/dbus/introspect
  go get -v github.com/godbus/dbus/prop
  go get -v github.com/Lofanmi/pinyin-golang/pinyin
  go get -v gopkg.in/yaml.v3
  go get -v gopkg.in/yaml.v2
  go get -v github.com/youpy/go-wav
  env GOPATH="$srcdir/build" go get -v golang.org/x/sys/unix
  go get -v github.com/mdlayher/netlink
  go get -v github.com/golang/protobuf/proto
  go get -v github.com/stretchr/testify
  go get -v github.com/jouyouyun/hardware
  sed -i 's#/usr/share/backgrounds/default_background.jpg#/usr/share/backgrounds/deepin/desktop.jpg#' accounts/user.go
}

build() {
  export CGO_CPPFLAGS="${CPPFLAGS}"
  export CGO_CFLAGS="${CFLAGS}"
  export CGO_CXXFLAGS="${CXXFLAGS}"
  export CGO_LDFLAGS="${LDFLAGS}"
  export GOFLAGS="-buildmode=pie -trimpath -mod=readonly -modcacherw"
  cd $deepin_source_name
#  make -C network/nm_generator gen-nm-code
  make
}

package() {
  cd $deepin_source_name
  make DESTDIR="$pkgdir" PAM_MODULE_DIR=usr/lib/security install

  mv "$pkgdir"{,/usr}/lib/systemd
  mv "$pkgdir"{,/usr}/lib/udev
  rmdir "$pkgdir"/lib

  install -Dm644 ../deepin-daemon.sysusers "$pkgdir/usr/lib/sysusers.d/deepin-daemon.conf"
}
