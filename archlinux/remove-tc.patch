diff --git a/bin/dde-session-daemon/daemon.go b/bin/dde-session-daemon/daemon.go
index 1a1a2d55..4285fc55 100644
--- a/bin/dde-session-daemon/daemon.go
+++ b/bin/dde-session-daemon/daemon.go
@@ -189,7 +189,6 @@ func (s *SessionDaemon) initModules() {
 		"screensaver",
 		"sessionwatcher",
 		"power", // need screensaver and sessionwatcher
-		"uadpagent",
 		"service-trigger",
 		"clipboard",
 		"keybinding",
diff --git a/bin/dde-session-daemon/module.go b/bin/dde-session-daemon/module.go
index c49d1a59..41c04468 100644
--- a/bin/dde-session-daemon/module.go
+++ b/bin/dde-session-daemon/module.go
@@ -37,7 +37,6 @@ import (
 	_ "github.com/linuxdeepin/dde-daemon/service_trigger"
 	_ "github.com/linuxdeepin/dde-daemon/session/eventlog"
 	_ "github.com/linuxdeepin/dde-daemon/session/power"
-	_ "github.com/linuxdeepin/dde-daemon/session/uadpagent"
 	_ "github.com/linuxdeepin/dde-daemon/sessionwatcher"
 	_ "github.com/linuxdeepin/dde-daemon/systeminfo"
 	_ "github.com/linuxdeepin/dde-daemon/timedate"
diff --git a/bin/dde-system-daemon/main.go b/bin/dde-system-daemon/main.go
index e39fa46b..39a517eb 100644
--- a/bin/dde-system-daemon/main.go
+++ b/bin/dde-system-daemon/main.go
@@ -27,7 +27,6 @@ import (
 	_ "github.com/linuxdeepin/dde-daemon/system/swapsched"
 	_ "github.com/linuxdeepin/dde-daemon/system/systeminfo"
 	_ "github.com/linuxdeepin/dde-daemon/system/timedated"
-	_ "github.com/linuxdeepin/dde-daemon/system/uadp"
         _ "github.com/linuxdeepin/dde-daemon/system/resource_ctl"
 
 	"github.com/linuxdeepin/dde-daemon/loader"
