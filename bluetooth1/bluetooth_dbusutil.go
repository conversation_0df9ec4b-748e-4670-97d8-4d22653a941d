// Code generated by "dbusutil-gen -type Bluetooth bluetooth.go"; DO NOT EDIT.

package bluetooth

func (v *Bluetooth) setPropState(value uint32) (changed bool) {
	if v.State != value {
		v.State = value
		v.emitPropChangedState(value)
		return true
	}
	return false
}

func (v *Bluetooth) emitPropChangedState(value uint32) error {
	return v.service.EmitPropertyChanged(v, "State", value)
}

func (v *Bluetooth) setPropTransportable(value bool) (changed bool) {
	if v.Transportable != value {
		v.Transportable = value
		v.emitPropChangedTransportable(value)
		return true
	}
	return false
}

func (v *Bluetooth) emitPropChangedTransportable(value bool) error {
	return v.service.EmitPropertyChanged(v, "Transportable", value)
}

func (v *Bluetooth) setPropCanSendFile(value bool) (changed bool) {
	if v.CanSendFile != value {
		v.CanSendFile = value
		v.emitPropChangedCanSendFile(value)
		return true
	}
	return false
}

func (v *Bluetooth) emitPropChangedCanSendFile(value bool) error {
	return v.service.EmitPropertyChanged(v, "CanSendFile", value)
}
