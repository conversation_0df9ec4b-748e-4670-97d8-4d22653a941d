// Code generated by "dbusutil-gen -type Manager -import github.com/godbus/dbus/v5 manager.go"; DO NOT EDIT.

package gesture1

func (v *Manager) setPropInfos(value GestureInfos) (changed bool) {
	if value != nil {
		v.Infos = value
		v.emitPropChangedInfos(value)
		return true
	}
	return false
}

func (v *Manager) emitPropChangedInfos(value GestureInfos) error {
	return v.service.EmitPropertyChanged(v, "Infos", value)
}
