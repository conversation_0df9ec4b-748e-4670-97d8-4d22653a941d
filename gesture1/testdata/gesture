[{"Event": {"Name": "swipe", "Direction": "up", "Fingers": 3}, "Action": {"Type": "built-in", "Action": "ToggleMaximize"}}, {"Event": {"Name": "swipe", "Direction": "down", "Fingers": 3}, "Action": {"Type": "built-in", "Action": "ToggleMaximize"}}, {"Event": {"Name": "swipe", "Direction": "left", "Fingers": 3}, "Action": {"Type": "built-in", "Action": "SplitWindowLeft"}}, {"Event": {"Name": "swipe", "Direction": "right", "Fingers": 3}, "Action": {"Type": "built-in", "Action": "SplitWindowRight"}}, {"Event": {"Name": "tap", "Direction": "none", "Fingers": 3}, "Action": {"Type": "shortcut", "Action": "ctrl+alt+u"}}, {"Event": {"Name": "swipe", "Direction": "up", "Fingers": 4}, "Action": {"Type": "built-in", "Action": "Handle4Or5FingersSwipeUp"}}, {"Event": {"Name": "swipe", "Direction": "down", "Fingers": 4}, "Action": {"Type": "built-in", "Action": "Handle4Or5FingersSwipeDown"}}, {"Event": {"Name": "swipe", "Direction": "right", "Fingers": 4}, "Action": {"Type": "built-in", "Action": "ReverseSwitchWorkspace"}}, {"Event": {"Name": "swipe", "Direction": "left", "Fingers": 4}, "Action": {"Type": "built-in", "Action": "SwitchWorkspace"}}, {"Event": {"Name": "tap", "Direction": "none", "Fingers": 4}, "Action": {"Type": "commandline", "Action": "dbus-send --type=method_call --dest=org.deepin.dde.Launcher1 /org/deepin/dde/Launcher1 org.deepin.dde.Launcher1.Toggle"}}, {"Event": {"Name": "swipe", "Direction": "up", "Fingers": 5}, "Action": {"Type": "built-in", "Action": "Handle4Or5FingersSwipeUp"}}, {"Event": {"Name": "swipe", "Direction": "down", "Fingers": 5}, "Action": {"Type": "built-in", "Action": "Handle4Or5FingersSwipeDown"}}, {"Event": {"Name": "swipe", "Direction": "right", "Fingers": 5}, "Action": {"Type": "built-in", "Action": "ReverseSwitchWorkspace"}}, {"Event": {"Name": "swipe", "Direction": "left", "Fingers": 5}, "Action": {"Type": "built-in", "Action": "SwitchWorkspace"}}, {"Event": {"Name": "tap", "Direction": "none", "Fingers": 5}, "Action": {"Type": "commandline", "Action": "dbus-send --type=method_call --dest=org.deepin.dde.Launcher1 /org/deepin/dde/Launcher1 org.deepin.dde.Launcher1.Toggle"}}]