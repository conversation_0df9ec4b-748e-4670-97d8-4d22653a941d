// SPDX-FileCopyrightText: 2022 UnionTech Software Technology Co., Ltd.
//
// SPDX-License-Identifier: GPL-3.0-or-later

// Code generated by mockery v1.0.0. DO NOT EDIT.

package mocks

import mock "github.com/stretchr/testify/mock"
import x "github.com/linuxdeepin/go-x11-client"

// XClient is an autogenerated mock type for the XClient type
type XClient struct {
	mock.Mock
}

// ChangePropertyE provides a mock function with given fields: mode, window, property, type0, format, data
func (_m *XClient) ChangePropertyE(mode uint8, window x.Window, property x.Atom, type0 x.Atom, format uint8, data []byte) error {
	ret := _m.Called(mode, window, property, type0, format, data)

	var r0 error
	if rf, ok := ret.Get(0).(func(uint8, x.Window, x.Atom, x.Atom, uint8, []byte) error); ok {
		r0 = rf(mode, window, property, type0, format, data)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// ChangeWindowEventMask provides a mock function with given fields: win, evMask
func (_m *XClient) ChangeWindowEventMask(win x.Window, evMask uint32) error {
	ret := _m.Called(win, evMask)

	var r0 error
	if rf, ok := ret.Get(0).(func(x.Window, uint32) error); ok {
		r0 = rf(win, evMask)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// Conn provides a mock function with given fields:
func (_m *XClient) Conn() *x.Conn {
	ret := _m.Called()

	var r0 *x.Conn
	if rf, ok := ret.Get(0).(func() *x.Conn); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*x.Conn)
		}
	}

	return r0
}

// ConvertSelection provides a mock function with given fields: requestor, selection, target, property, time
func (_m *XClient) ConvertSelection(requestor x.Window, selection x.Atom, target x.Atom, property x.Atom, time x.Timestamp) {
	_m.Called(requestor, selection, target, property, time)
}

// ConvertSelectionE provides a mock function with given fields: requestor, selection, target, property, time
func (_m *XClient) ConvertSelectionE(requestor x.Window, selection x.Atom, target x.Atom, property x.Atom, time x.Timestamp) error {
	ret := _m.Called(requestor, selection, target, property, time)

	var r0 error
	if rf, ok := ret.Get(0).(func(x.Window, x.Atom, x.Atom, x.Atom, x.Timestamp) error); ok {
		r0 = rf(requestor, selection, target, property, time)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// CreateWindow provides a mock function with given fields:
func (_m *XClient) CreateWindow() (x.Window, error) {
	ret := _m.Called()

	var r0 x.Window
	if rf, ok := ret.Get(0).(func() x.Window); ok {
		r0 = rf()
	} else {
		r0 = ret.Get(0).(x.Window)
	}

	var r1 error
	if rf, ok := ret.Get(1).(func() error); ok {
		r1 = rf()
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// DeletePropertyE provides a mock function with given fields: window, property
func (_m *XClient) DeletePropertyE(window x.Window, property x.Atom) error {
	ret := _m.Called(window, property)

	var r0 error
	if rf, ok := ret.Get(0).(func(x.Window, x.Atom) error); ok {
		r0 = rf(window, property)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// Flush provides a mock function with given fields:
func (_m *XClient) Flush() error {
	ret := _m.Called()

	var r0 error
	if rf, ok := ret.Get(0).(func() error); ok {
		r0 = rf()
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// GetAtom provides a mock function with given fields: name
func (_m *XClient) GetAtom(name string) (x.Atom, error) {
	ret := _m.Called(name)

	var r0 x.Atom
	if rf, ok := ret.Get(0).(func(string) x.Atom); ok {
		r0 = rf(name)
	} else {
		r0 = ret.Get(0).(x.Atom)
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(string) error); ok {
		r1 = rf(name)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetAtomName provides a mock function with given fields: atom
func (_m *XClient) GetAtomName(atom x.Atom) (string, error) {
	ret := _m.Called(atom)

	var r0 string
	if rf, ok := ret.Get(0).(func(x.Atom) string); ok {
		r0 = rf(atom)
	} else {
		r0 = ret.Get(0).(string)
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(x.Atom) error); ok {
		r1 = rf(atom)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetProperty provides a mock function with given fields: delete, window, property, type0, longOffset, longLength
func (_m *XClient) GetProperty(delete bool, window x.Window, property x.Atom, type0 x.Atom, longOffset uint32, longLength uint32) (*x.GetPropertyReply, error) {
	ret := _m.Called(delete, window, property, type0, longOffset, longLength)

	var r0 *x.GetPropertyReply
	if rf, ok := ret.Get(0).(func(bool, x.Window, x.Atom, x.Atom, uint32, uint32) *x.GetPropertyReply); ok {
		r0 = rf(delete, window, property, type0, longOffset, longLength)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*x.GetPropertyReply)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(bool, x.Window, x.Atom, x.Atom, uint32, uint32) error); ok {
		r1 = rf(delete, window, property, type0, longOffset, longLength)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetSelectionOwner provides a mock function with given fields: selection
func (_m *XClient) GetSelectionOwner(selection x.Atom) (x.Window, error) {
	ret := _m.Called(selection)

	var r0 x.Window
	if rf, ok := ret.Get(0).(func(x.Atom) x.Window); ok {
		r0 = rf(selection)
	} else {
		r0 = ret.Get(0).(x.Window)
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(x.Atom) error); ok {
		r1 = rf(selection)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// SelectSelectionInputE provides a mock function with given fields: window, selection, eventMask
func (_m *XClient) SelectSelectionInputE(window x.Window, selection x.Atom, eventMask uint32) error {
	ret := _m.Called(window, selection, eventMask)

	var r0 error
	if rf, ok := ret.Get(0).(func(x.Window, x.Atom, uint32) error); ok {
		r0 = rf(window, selection, eventMask)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// SendEventE provides a mock function with given fields: propagate, destination, eventMask, event
func (_m *XClient) SendEventE(propagate bool, destination x.Window, eventMask uint32, event interface{}) error {
	ret := _m.Called(propagate, destination, eventMask, event)

	var r0 error
	if rf, ok := ret.Get(0).(func(bool, x.Window, uint32, interface{}) error); ok {
		r0 = rf(propagate, destination, eventMask, event)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// SetSelectionOwner provides a mock function with given fields: owner, selection, time
func (_m *XClient) SetSelectionOwner(owner x.Window, selection x.Atom, time x.Timestamp) {
	_m.Called(owner, selection, time)
}

// SetSelectionOwnerE provides a mock function with given fields: owner, selection, time
func (_m *XClient) SetSelectionOwnerE(owner x.Window, selection x.Atom, time x.Timestamp) error {
	ret := _m.Called(owner, selection, time)

	var r0 error
	if rf, ok := ret.Get(0).(func(x.Window, x.Atom, x.Timestamp) error); ok {
		r0 = rf(owner, selection, time)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}
