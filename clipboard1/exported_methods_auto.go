// Code generated by "dbusutil-gen em -type Manager"; DO NOT EDIT.

package clipboard

import (
	"github.com/linuxdeepin/go-lib/dbusutil"
)

func (v *Manager) GetExportedMethods() dbusutil.ExportedMethods {
	return dbusutil.ExportedMethods{
		{
			Name: "BecomeClipboardOwner",
			Fn:   v.BecomeClipboardOwner,
		},
		{
			Name:   "RemoveTarget",
			Fn:     v.RemoveTarget,
			InArgs: []string{"target"},
		},
		{
			Name: "SaveClipboard",
			Fn:   v.SaveClipboard,
		},
		{
			Name: "WriteContent",
			Fn:   v.WriteContent,
		},
	}
}
