// Code generated by "dbusutil-gen em -type Manager,User,ImageBlur"; DO NOT EDIT.

package accounts

import (
	"github.com/linuxdeepin/go-lib/dbusutil"
)

func (v *ImageBlur) GetExportedMethods() dbusutil.ExportedMethods {
	return dbusutil.ExportedMethods{
		{
			Name:   "Delete",
			Fn:     v.Delete,
			InArgs: []string{"file"},
		},
		{
			Name:    "Get",
			Fn:      v.Get,
			InArgs:  []string{"file"},
			OutArgs: []string{"blurred"},
		},
	}
}
func (v *Manager) GetExportedMethods() dbusutil.ExportedMethods {
	return dbusutil.ExportedMethods{
		{
			Name:   "AllowGuestAccount",
			Fn:     v.AllowGuestAccount,
			InArgs: []string{"allow"},
		},
		{
			Name:   "CreateGroup",
			Fn:     v.CreateGroup,
			InArgs: []string{"groupName", "gid", "isSystem"},
		},
		{
			Name:    "CreateGuestAccount",
			Fn:      v.CreateGuestAccount,
			OutArgs: []string{"user"},
		},
		{
			Name:    "CreateUser",
			Fn:      v.<PERSON>,
			InArgs:  []string{"name", "fullName", "accountType"},
			OutArgs: []string{"userPath"},
		},
		{
			Name:   "DeleteGroup",
			Fn:     v.DeleteGroup,
			InArgs: []string{"groupName", "force"},
		},
		{
			Name:   "DeleteUser",
			Fn:     v.DeleteUser,
			InArgs: []string{"name", "rmFiles"},
		},
		{
			Name:   "EnablePasswdChangedHandler",
			Fn:     v.EnablePasswdChangedHandler,
			InArgs: []string{"enable"},
		},
		{
			Name:    "FindUserById",
			Fn:      v.FindUserById,
			InArgs:  []string{"uid"},
			OutArgs: []string{"user"},
		},
		{
			Name:    "FindUserByName",
			Fn:      v.FindUserByName,
			InArgs:  []string{"name"},
			OutArgs: []string{"user"},
		},
		{
			Name:    "GetGroupInfoByName",
			Fn:      v.GetGroupInfoByName,
			InArgs:  []string{"name"},
			OutArgs: []string{"groupInfo"},
		},
		{
			Name:    "GetGroups",
			Fn:      v.GetGroups,
			OutArgs: []string{"groups"},
		},
		{
			Name:    "GetPresetGroups",
			Fn:      v.GetPresetGroups,
			InArgs:  []string{"accountType"},
			OutArgs: []string{"groups"},
		},
		{
			Name:    "IsPasswordValid",
			Fn:      v.IsPasswordValid,
			InArgs:  []string{"password"},
			OutArgs: []string{"valid", "msg", "code"},
		},
		{
			Name:    "IsUsernameValid",
			Fn:      v.IsUsernameValid,
			InArgs:  []string{"name"},
			OutArgs: []string{"valid", "msg", "code"},
		},
		{
			Name:   "ModifyGroup",
			Fn:     v.ModifyGroup,
			InArgs: []string{"currentGroupName", "newGroupName", "newGID"},
		},
		{
			Name:    "RandUserIcon",
			Fn:      v.RandUserIcon,
			OutArgs: []string{"iconFile"},
		},
		{
			Name:   "SetTerminalLocked",
			Fn:     v.SetTerminalLocked,
			InArgs: []string{"locked"},
		},
	}
}
func (v *User) GetExportedMethods() dbusutil.ExportedMethods {
	return dbusutil.ExportedMethods{
		{
			Name:   "AddGroup",
			Fn:     v.AddGroup,
			InArgs: []string{"group"},
		},
		{
			Name:   "DeleteGroup",
			Fn:     v.DeleteGroup,
			InArgs: []string{"group"},
		},
		{
			Name:   "DeleteIconFile",
			Fn:     v.DeleteIconFile,
			InArgs: []string{"icon"},
		},
		{
			Name: "DeleteSecretKey",
			Fn:   v.DeleteSecretKey,
		},
		{
			Name:   "EnableNoPasswdLogin",
			Fn:     v.EnableNoPasswdLogin,
			InArgs: []string{"enabled"},
		},
		{
			Name:   "EnableWechatAuth",
			Fn:     v.EnableWechatAuth,
			InArgs: []string{"value"},
		},
		{
			Name:    "GetReminderInfo",
			Fn:      v.GetReminderInfo,
			OutArgs: []string{"info"},
		},
		{
			Name:    "GetSecretKey",
			Fn:      v.GetSecretKey,
			InArgs:  []string{"username"},
			OutArgs: []string{"outArg0"},
		},
		{
			Name:    "GetSecretQuestions",
			Fn:      v.GetSecretQuestions,
			OutArgs: []string{"list"},
		},
		{
			Name:    "IsPasswordExpired",
			Fn:      v.IsPasswordExpired,
			OutArgs: []string{"outArg0"},
		},
		{
			Name:    "PasswordExpiredInfo",
			Fn:      v.PasswordExpiredInfo,
			OutArgs: []string{"expiredStatus", "dayLeft"},
		},
		{
			Name:   "SetAutomaticLogin",
			Fn:     v.SetAutomaticLogin,
			InArgs: []string{"enabled"},
		},
		{
			Name:   "SetCurrentWorkspace",
			Fn:     v.SetCurrentWorkspace,
			InArgs: []string{"currentWorkspace"},
		},
		{
			Name:   "SetDesktopBackgrounds",
			Fn:     v.SetDesktopBackgrounds,
			InArgs: []string{"val"},
		},
		{
			Name:   "SetFullName",
			Fn:     v.SetFullName,
			InArgs: []string{"name"},
		},
		{
			Name:   "SetGreeterBackground",
			Fn:     v.SetGreeterBackground,
			InArgs: []string{"bg"},
		},
		{
			Name:   "SetGroups",
			Fn:     v.SetGroups,
			InArgs: []string{"groups"},
		},
		{
			Name:   "SetHistoryLayout",
			Fn:     v.SetHistoryLayout,
			InArgs: []string{"list"},
		},
		{
			Name:   "SetHomeDir",
			Fn:     v.SetHomeDir,
			InArgs: []string{"home"},
		},
		{
			Name:   "SetIconFile",
			Fn:     v.SetIconFile,
			InArgs: []string{"iconURI"},
		},
		{
			Name:   "SetLayout",
			Fn:     v.SetLayout,
			InArgs: []string{"layout"},
		},
		{
			Name:   "SetLocale",
			Fn:     v.SetLocale,
			InArgs: []string{"locale"},
		},
		{
			Name:   "SetLocked",
			Fn:     v.SetLocked,
			InArgs: []string{"locked"},
		},
		{
			Name:   "SetLongDateFormat",
			Fn:     v.SetLongDateFormat,
			InArgs: []string{"value"},
		},
		{
			Name:   "SetLongTimeFormat",
			Fn:     v.SetLongTimeFormat,
			InArgs: []string{"value"},
		},
		{
			Name:   "SetMaxPasswordAge",
			Fn:     v.SetMaxPasswordAge,
			InArgs: []string{"nDays"},
		},
		{
			Name:   "SetPassword",
			Fn:     v.SetPassword,
			InArgs: []string{"password"},
		},
		{
			Name:   "SetPasswordHint",
			Fn:     v.SetPasswordHint,
			InArgs: []string{"hint"},
		},
		{
			Name:   "SetQuickLogin",
			Fn:     v.SetQuickLogin,
			InArgs: []string{"enabled"},
		},
		{
			Name:   "SetSecretKey",
			Fn:     v.SetSecretKey,
			InArgs: []string{"secretKey"},
		},
		{
			Name:   "SetSecretQuestions",
			Fn:     v.SetSecretQuestions,
			InArgs: []string{"list"},
		},
		{
			Name:   "SetShell",
			Fn:     v.SetShell,
			InArgs: []string{"shell"},
		},
		{
			Name:   "SetShortDateFormat",
			Fn:     v.SetShortDateFormat,
			InArgs: []string{"value"},
		},
		{
			Name:   "SetShortTimeFormat",
			Fn:     v.SetShortTimeFormat,
			InArgs: []string{"value"},
		},
		{
			Name:   "SetUse24HourFormat",
			Fn:     v.SetUse24HourFormat,
			InArgs: []string{"value"},
		},
		{
			Name:   "SetWeekBegins",
			Fn:     v.SetWeekBegins,
			InArgs: []string{"value"},
		},
		{
			Name:   "SetWeekdayFormat",
			Fn:     v.SetWeekdayFormat,
			InArgs: []string{"value"},
		},
		{
			Name: "UpdateWechatAuthState",
			Fn:   v.UpdateWechatAuthState,
		},
		{
			Name:    "VerifySecretQuestions",
			Fn:      v.VerifySecretQuestions,
			InArgs:  []string{"answers"},
			OutArgs: []string{"failed"},
		},
	}
}
