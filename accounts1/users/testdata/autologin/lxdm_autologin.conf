[base]
## uncomment and set autologin username to enable autologin
# autologin=dgod
autologin=wen

## uncomment and set timeout to enable timeout autologin,
## the value should >=5
# timeout=10

## default session or desktop used when no systemwide config
# session=/usr/bin/startlxde

## uncomment and set to set numlock on your keyboard
# numlock=0

## set this if you don't want to put xauth file at ~/.Xauthority
# xauth_path=/tmp

# not ask password for users who have empty password
# skip_password=1

## greeter used to welcome the user
greeter=/usr/libexec/lxdm-greeter-gtk

[server]
## arg used to start xserver, not fully function
# arg=/usr/bin/X -background vt1
# uncomment this if you really want xserver listen to tcp
# tcp_listen=1
# uncoment this if you want reset the xserver after logou
# reset=1

[display]
## gtk theme used by greeter
gtk_theme=Clearlooks

## background of the greeter
bg=/usr/share/backgrounds/default.png

## if show bottom pane
bottom_pane=1

## if show language select control
lang=1

## if show keyboard layout select control
keyboard=0

## the theme of greeter
theme=Industrial

[input]

[userlist]
## if disable the user list control at greeter
disable=0

## whitelist user
white=

## blacklist user
black=

