// Code generated by "dbusutil-gen em -type ALRecorder,DFWatcher"; DO NOT EDIT.

package apps1

import (
	"github.com/linuxdeepin/go-lib/dbusutil"
)

func (v *ALRecorder) GetExportedMethods() dbusutil.ExportedMethods {
	return dbusutil.ExportedMethods{
		{
			Name:    "GetNew",
			Fn:      v.GetNew,
			OutArgs: []string{"newApps"},
		},
		{
			Name:   "MarkLaunched",
			Fn:     v.Mark<PERSON>aunched,
			InArgs: []string{"file"},
		},
		{
			Name:   "UninstallHints",
			Fn:     v.UninstallHints,
			InArgs: []string{"desktopFiles"},
		},
		{
			Name:   "WatchDirs",
			Fn:     v.WatchDirs,
			InArgs: []string{"dataDirs"},
		},
	}
}
func (v *DFWatcher) GetExportedMethods() dbusutil.ExportedMethods {
	return nil
}
