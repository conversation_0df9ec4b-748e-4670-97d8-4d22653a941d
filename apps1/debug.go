//go:build debug
// +build debug

// SPDX-FileCopyrightText: 2018 - 2022 UnionTech Software Technology Co., Ltd.
//
// SPDX-License-Identifier: GPL-3.0-or-later

package apps1

//func (r *ALRecorder) DebugUserRemoved(sender dbus.Sender) *dbus.Error {
//	uid, err := r.Service().GetConnUID(string(sender))
//	if err != nil {
//		return dbusutil.ToError(err)
//	}
//	r.handleUserRemoved(int(uid))
//	return nil
//}
