[main]
host = https://www.transifex.com
minimum_perc = 80
mode = developer

[o:linuxdeepin:p:deepin-desktop-environment:r:dde-daemonpot]
file_filter = misc/po/<lang>.po
minimum_perc = 0
source_file = misc/po/dde-daemon.pot
source_lang = en
type = PO

[o:linuxdeepin:p:deepin-desktop-environment:r:dde-daemon-grub2-policyts]
file_filter = misc/ts/org.deepin.dde.grub2.policy/policy_<lang>.ts
source_file = misc/ts/org.deepin.dde.grub2.policy/policy.ts
source_lang = en
type = QT

[o:linuxdeepin:p:deepin-desktop-environment:r:dde-daemon-accounts-policyts]
file_filter = misc/ts/org.deepin.dde.accounts.policy/policy_<lang>.ts
source_file = misc/ts/org.deepin.dde.accounts.policy/policy.ts
source_lang = en
type = QT

[o:linuxdeepin:p:deepin-desktop-environment:r:dde-daemon-fprintd-policyts]
file_filter = misc/ts/org.deepin.dde.fprintd.policy/policy_<lang>.ts
source_file = misc/ts/org.deepin.dde.fprintd.policy/policy.ts
source_lang = en
type = QT
