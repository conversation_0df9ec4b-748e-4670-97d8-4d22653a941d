Format: https://www.debian.org/doc/packaging-manuals/copyright-format/1.0/
Upstream-Name: dde-daemon
Upstream-Contact: UnionTech Software Technology Co., Ltd.  <>
Source: https://github.com/linuxdeepin/dde-daemon

# gitignore clang-format gitreview .tx
Files: .gitignore .clang-format .gitreview .mailmap .tx/config
Copyright: None
License: CC0-1.0

# ci
Files: .github/* .obs/* .gitlab-ci.yml
Copyright: None
License: CC0-1.0

# debian rpm archlinux
Files: debian/* rpm/* archlinux/*
Copyright: None
License: CC0-1.0

# README
Files: *.md graph/README README.zh_CN.md
Copyright: UnionTech Software Technology Co., Ltd.
License: CC-BY-4.0

# org xml json yaml html yml css
Files: *.org *.xml *.json *.yaml *.html *.yml *.css *.devhelp2
Copyright: UnionTech Software Technology Co., Ltd.
License: GPL-3.0-or-later

# docs
Files: docs/*
Copyright: UnionTech Software Technology Co., Ltd.
License: GPL-3.0-or-later

# Project file
Files: *Makefile*
Copyright: None
License: CC0-1.0

# sh
Files: gen.sh _tool/prop_gen/gen.sh gen_test_report.sh
Copyright: None
License: GPL-3.0-or-later

# auto generation files
Files: *_methods_auto.go *_dbusutil.go *_gen.go
Copyright: UnionTech Software Technology Co., Ltd.
License: GPL-3.0-or-later

# translation or configuration files
Files: misc/* *.mod *.sum
Copyright: UnionTech Software Technology Co., Ltd.
License: GPL-3.0-or-later

# png svg
Files: *.png *.svg *.jpg
Copyright: UnionTech Software Technology Co., Ltd.
License: GPL-3.0-or-later

# configuration file
Files: *.conf *.gir
Copyright: UnionTech Software Technology Co., Ltd.
License: GPL-3.0-or-later

# test file or examples
Files: *testdata/* *examples/*
Copyright: UnionTech Software Technology Co., Ltd.
License: GPL-3.0-or-later

# linux external file
Files: iw/nl80211.h
Copyright: UnionTech Software Technology Co., Ltd.
License: ISC
