// SPDX-FileCopyrightText: 2022 UnionTech Software Technology Co., Ltd.
//
// SPDX-License-Identifier: GPL-3.0-or-later

package iso639

type language struct {
	A3B string
	A3T string
	A2  string
}

var allLanguages = []language{
	{
		A3B: "aar",
		A3T: "aar",
		A2:  "aa",
	},
	{
		A3B: "abk",
		A3T: "abk",
		A2:  "ab",
	},
	{
		A3B: "ace",
		A3T: "ace",
		A2:  "",
	},
	{
		A3B: "ach",
		A3T: "ach",
		A2:  "",
	},
	{
		A3B: "ada",
		A3T: "ada",
		A2:  "",
	},
	{
		A3B: "ady",
		A3T: "ady",
		A2:  "",
	},
	{
		A3B: "afa",
		A3T: "afa",
		A2:  "",
	},
	{
		A3B: "afh",
		A3T: "afh",
		A2:  "",
	},
	{
		A3B: "afr",
		A3T: "afr",
		A2:  "af",
	},
	{
		A3B: "ain",
		A3T: "ain",
		A2:  "",
	},
	{
		A3B: "aka",
		A3T: "aka",
		A2:  "ak",
	},
	{
		A3B: "akk",
		A3T: "akk",
		A2:  "",
	},
	{
		A3B: "alb",
		A3T: "sqi",
		A2:  "sq",
	},
	{
		A3B: "ale",
		A3T: "ale",
		A2:  "",
	},
	{
		A3B: "alg",
		A3T: "alg",
		A2:  "",
	},
	{
		A3B: "alt",
		A3T: "alt",
		A2:  "",
	},
	{
		A3B: "amh",
		A3T: "amh",
		A2:  "am",
	},
	{
		A3B: "ang",
		A3T: "ang",
		A2:  "",
	},
	{
		A3B: "anp",
		A3T: "anp",
		A2:  "",
	},
	{
		A3B: "apa",
		A3T: "apa",
		A2:  "",
	},
	{
		A3B: "ara",
		A3T: "ara",
		A2:  "ar",
	},
	{
		A3B: "arc",
		A3T: "arc",
		A2:  "",
	},
	{
		A3B: "arg",
		A3T: "arg",
		A2:  "an",
	},
	{
		A3B: "arm",
		A3T: "hye",
		A2:  "hy",
	},
	{
		A3B: "arn",
		A3T: "arn",
		A2:  "",
	},
	{
		A3B: "arp",
		A3T: "arp",
		A2:  "",
	},
	{
		A3B: "art",
		A3T: "art",
		A2:  "",
	},
	{
		A3B: "arw",
		A3T: "arw",
		A2:  "",
	},
	{
		A3B: "asm",
		A3T: "asm",
		A2:  "as",
	},
	{
		A3B: "ast",
		A3T: "ast",
		A2:  "",
	},
	{
		A3B: "ath",
		A3T: "ath",
		A2:  "",
	},
	{
		A3B: "aus",
		A3T: "aus",
		A2:  "",
	},
	{
		A3B: "ava",
		A3T: "ava",
		A2:  "av",
	},
	{
		A3B: "ave",
		A3T: "ave",
		A2:  "ae",
	},
	{
		A3B: "awa",
		A3T: "awa",
		A2:  "",
	},
	{
		A3B: "aym",
		A3T: "aym",
		A2:  "ay",
	},
	{
		A3B: "aze",
		A3T: "aze",
		A2:  "az",
	},
	{
		A3B: "bad",
		A3T: "bad",
		A2:  "",
	},
	{
		A3B: "bai",
		A3T: "bai",
		A2:  "",
	},
	{
		A3B: "bak",
		A3T: "bak",
		A2:  "ba",
	},
	{
		A3B: "bal",
		A3T: "bal",
		A2:  "",
	},
	{
		A3B: "bam",
		A3T: "bam",
		A2:  "bm",
	},
	{
		A3B: "ban",
		A3T: "ban",
		A2:  "",
	},
	{
		A3B: "baq",
		A3T: "eus",
		A2:  "eu",
	},
	{
		A3B: "bas",
		A3T: "bas",
		A2:  "",
	},
	{
		A3B: "bat",
		A3T: "bat",
		A2:  "",
	},
	{
		A3B: "bej",
		A3T: "bej",
		A2:  "",
	},
	{
		A3B: "bel",
		A3T: "bel",
		A2:  "be",
	},
	{
		A3B: "bem",
		A3T: "bem",
		A2:  "",
	},
	{
		A3B: "ben",
		A3T: "ben",
		A2:  "bn",
	},
	{
		A3B: "ber",
		A3T: "ber",
		A2:  "",
	},
	{
		A3B: "bho",
		A3T: "bho",
		A2:  "",
	},
	{
		A3B: "bih",
		A3T: "bih",
		A2:  "bh",
	},
	{
		A3B: "bik",
		A3T: "bik",
		A2:  "",
	},
	{
		A3B: "bin",
		A3T: "bin",
		A2:  "",
	},
	{
		A3B: "bis",
		A3T: "bis",
		A2:  "bi",
	},
	{
		A3B: "bla",
		A3T: "bla",
		A2:  "",
	},
	{
		A3B: "bnt",
		A3T: "bnt",
		A2:  "",
	},
	{
		A3B: "bos",
		A3T: "bos",
		A2:  "bs",
	},
	{
		A3B: "bra",
		A3T: "bra",
		A2:  "",
	},
	{
		A3B: "bre",
		A3T: "bre",
		A2:  "br",
	},
	{
		A3B: "btk",
		A3T: "btk",
		A2:  "",
	},
	{
		A3B: "bua",
		A3T: "bua",
		A2:  "",
	},
	{
		A3B: "bug",
		A3T: "bug",
		A2:  "",
	},
	{
		A3B: "bul",
		A3T: "bul",
		A2:  "bg",
	},
	{
		A3B: "bur",
		A3T: "mya",
		A2:  "my",
	},
	{
		A3B: "byn",
		A3T: "byn",
		A2:  "",
	},
	{
		A3B: "cad",
		A3T: "cad",
		A2:  "",
	},
	{
		A3B: "cai",
		A3T: "cai",
		A2:  "",
	},
	{
		A3B: "car",
		A3T: "car",
		A2:  "",
	},
	{
		A3B: "cat",
		A3T: "cat",
		A2:  "ca",
	},
	{
		A3B: "cau",
		A3T: "cau",
		A2:  "",
	},
	{
		A3B: "ceb",
		A3T: "ceb",
		A2:  "",
	},
	{
		A3B: "cel",
		A3T: "cel",
		A2:  "",
	},
	{
		A3B: "cha",
		A3T: "cha",
		A2:  "ch",
	},
	{
		A3B: "chb",
		A3T: "chb",
		A2:  "",
	},
	{
		A3B: "che",
		A3T: "che",
		A2:  "ce",
	},
	{
		A3B: "chg",
		A3T: "chg",
		A2:  "",
	},
	{
		A3B: "chi",
		A3T: "zho",
		A2:  "zh",
	},
	{
		A3B: "chk",
		A3T: "chk",
		A2:  "",
	},
	{
		A3B: "chm",
		A3T: "chm",
		A2:  "",
	},
	{
		A3B: "chn",
		A3T: "chn",
		A2:  "",
	},
	{
		A3B: "cho",
		A3T: "cho",
		A2:  "",
	},
	{
		A3B: "chp",
		A3T: "chp",
		A2:  "",
	},
	{
		A3B: "chr",
		A3T: "chr",
		A2:  "",
	},
	{
		A3B: "chu",
		A3T: "chu",
		A2:  "cu",
	},
	{
		A3B: "chv",
		A3T: "chv",
		A2:  "cv",
	},
	{
		A3B: "chy",
		A3T: "chy",
		A2:  "",
	},
	{
		A3B: "cmc",
		A3T: "cmc",
		A2:  "",
	},
	{
		A3B: "cnr",
		A3T: "cnr",
		A2:  "",
	},
	{
		A3B: "cop",
		A3T: "cop",
		A2:  "",
	},
	{
		A3B: "cor",
		A3T: "cor",
		A2:  "kw",
	},
	{
		A3B: "cos",
		A3T: "cos",
		A2:  "co",
	},
	{
		A3B: "cpe",
		A3T: "cpe",
		A2:  "",
	},
	{
		A3B: "cpf",
		A3T: "cpf",
		A2:  "",
	},
	{
		A3B: "cpp",
		A3T: "cpp",
		A2:  "",
	},
	{
		A3B: "cre",
		A3T: "cre",
		A2:  "cr",
	},
	{
		A3B: "crh",
		A3T: "crh",
		A2:  "",
	},
	{
		A3B: "crp",
		A3T: "crp",
		A2:  "",
	},
	{
		A3B: "csb",
		A3T: "csb",
		A2:  "",
	},
	{
		A3B: "cus",
		A3T: "cus",
		A2:  "",
	},
	{
		A3B: "cze",
		A3T: "ces",
		A2:  "cs",
	},
	{
		A3B: "dak",
		A3T: "dak",
		A2:  "",
	},
	{
		A3B: "dan",
		A3T: "dan",
		A2:  "da",
	},
	{
		A3B: "dar",
		A3T: "dar",
		A2:  "",
	},
	{
		A3B: "day",
		A3T: "day",
		A2:  "",
	},
	{
		A3B: "del",
		A3T: "del",
		A2:  "",
	},
	{
		A3B: "den",
		A3T: "den",
		A2:  "",
	},
	{
		A3B: "dgr",
		A3T: "dgr",
		A2:  "",
	},
	{
		A3B: "din",
		A3T: "din",
		A2:  "",
	},
	{
		A3B: "div",
		A3T: "div",
		A2:  "dv",
	},
	{
		A3B: "doi",
		A3T: "doi",
		A2:  "",
	},
	{
		A3B: "dra",
		A3T: "dra",
		A2:  "",
	},
	{
		A3B: "dsb",
		A3T: "dsb",
		A2:  "",
	},
	{
		A3B: "dua",
		A3T: "dua",
		A2:  "",
	},
	{
		A3B: "dum",
		A3T: "dum",
		A2:  "",
	},
	{
		A3B: "dut",
		A3T: "nld",
		A2:  "nl",
	},
	{
		A3B: "dyu",
		A3T: "dyu",
		A2:  "",
	},
	{
		A3B: "dzo",
		A3T: "dzo",
		A2:  "dz",
	},
	{
		A3B: "efi",
		A3T: "efi",
		A2:  "",
	},
	{
		A3B: "egy",
		A3T: "egy",
		A2:  "",
	},
	{
		A3B: "eka",
		A3T: "eka",
		A2:  "",
	},
	{
		A3B: "elx",
		A3T: "elx",
		A2:  "",
	},
	{
		A3B: "eng",
		A3T: "eng",
		A2:  "en",
	},
	{
		A3B: "enm",
		A3T: "enm",
		A2:  "",
	},
	{
		A3B: "epo",
		A3T: "epo",
		A2:  "eo",
	},
	{
		A3B: "est",
		A3T: "est",
		A2:  "et",
	},
	{
		A3B: "ewe",
		A3T: "ewe",
		A2:  "ee",
	},
	{
		A3B: "ewo",
		A3T: "ewo",
		A2:  "",
	},
	{
		A3B: "fan",
		A3T: "fan",
		A2:  "",
	},
	{
		A3B: "fao",
		A3T: "fao",
		A2:  "fo",
	},
	{
		A3B: "fat",
		A3T: "fat",
		A2:  "",
	},
	{
		A3B: "fij",
		A3T: "fij",
		A2:  "fj",
	},
	{
		A3B: "fil",
		A3T: "fil",
		A2:  "",
	},
	{
		A3B: "fin",
		A3T: "fin",
		A2:  "fi",
	},
	{
		A3B: "fiu",
		A3T: "fiu",
		A2:  "",
	},
	{
		A3B: "fon",
		A3T: "fon",
		A2:  "",
	},
	{
		A3B: "fre",
		A3T: "fra",
		A2:  "fr",
	},
	{
		A3B: "frm",
		A3T: "frm",
		A2:  "",
	},
	{
		A3B: "fro",
		A3T: "fro",
		A2:  "",
	},
	{
		A3B: "frr",
		A3T: "frr",
		A2:  "",
	},
	{
		A3B: "frs",
		A3T: "frs",
		A2:  "",
	},
	{
		A3B: "fry",
		A3T: "fry",
		A2:  "fy",
	},
	{
		A3B: "ful",
		A3T: "ful",
		A2:  "ff",
	},
	{
		A3B: "fur",
		A3T: "fur",
		A2:  "",
	},
	{
		A3B: "gaa",
		A3T: "gaa",
		A2:  "",
	},
	{
		A3B: "gay",
		A3T: "gay",
		A2:  "",
	},
	{
		A3B: "gba",
		A3T: "gba",
		A2:  "",
	},
	{
		A3B: "gem",
		A3T: "gem",
		A2:  "",
	},
	{
		A3B: "geo",
		A3T: "kat",
		A2:  "ka",
	},
	{
		A3B: "ger",
		A3T: "deu",
		A2:  "de",
	},
	{
		A3B: "gez",
		A3T: "gez",
		A2:  "",
	},
	{
		A3B: "gil",
		A3T: "gil",
		A2:  "",
	},
	{
		A3B: "gla",
		A3T: "gla",
		A2:  "gd",
	},
	{
		A3B: "gle",
		A3T: "gle",
		A2:  "ga",
	},
	{
		A3B: "glg",
		A3T: "glg",
		A2:  "gl",
	},
	{
		A3B: "glv",
		A3T: "glv",
		A2:  "gv",
	},
	{
		A3B: "gmh",
		A3T: "gmh",
		A2:  "",
	},
	{
		A3B: "goh",
		A3T: "goh",
		A2:  "",
	},
	{
		A3B: "gon",
		A3T: "gon",
		A2:  "",
	},
	{
		A3B: "gor",
		A3T: "gor",
		A2:  "",
	},
	{
		A3B: "got",
		A3T: "got",
		A2:  "",
	},
	{
		A3B: "grb",
		A3T: "grb",
		A2:  "",
	},
	{
		A3B: "grc",
		A3T: "grc",
		A2:  "",
	},
	{
		A3B: "gre",
		A3T: "ell",
		A2:  "el",
	},
	{
		A3B: "grn",
		A3T: "grn",
		A2:  "gn",
	},
	{
		A3B: "gsw",
		A3T: "gsw",
		A2:  "",
	},
	{
		A3B: "guj",
		A3T: "guj",
		A2:  "gu",
	},
	{
		A3B: "gwi",
		A3T: "gwi",
		A2:  "",
	},
	{
		A3B: "hai",
		A3T: "hai",
		A2:  "",
	},
	{
		A3B: "hat",
		A3T: "hat",
		A2:  "ht",
	},
	{
		A3B: "hau",
		A3T: "hau",
		A2:  "ha",
	},
	{
		A3B: "haw",
		A3T: "haw",
		A2:  "",
	},
	{
		A3B: "heb",
		A3T: "heb",
		A2:  "he",
	},
	{
		A3B: "her",
		A3T: "her",
		A2:  "hz",
	},
	{
		A3B: "hil",
		A3T: "hil",
		A2:  "",
	},
	{
		A3B: "him",
		A3T: "him",
		A2:  "",
	},
	{
		A3B: "hin",
		A3T: "hin",
		A2:  "hi",
	},
	{
		A3B: "hit",
		A3T: "hit",
		A2:  "",
	},
	{
		A3B: "hmn",
		A3T: "hmn",
		A2:  "",
	},
	{
		A3B: "hmo",
		A3T: "hmo",
		A2:  "ho",
	},
	{
		A3B: "hrv",
		A3T: "hrv",
		A2:  "hr",
	},
	{
		A3B: "hsb",
		A3T: "hsb",
		A2:  "",
	},
	{
		A3B: "hun",
		A3T: "hun",
		A2:  "hu",
	},
	{
		A3B: "hup",
		A3T: "hup",
		A2:  "",
	},
	{
		A3B: "iba",
		A3T: "iba",
		A2:  "",
	},
	{
		A3B: "ibo",
		A3T: "ibo",
		A2:  "ig",
	},
	{
		A3B: "ice",
		A3T: "isl",
		A2:  "is",
	},
	{
		A3B: "ido",
		A3T: "ido",
		A2:  "io",
	},
	{
		A3B: "iii",
		A3T: "iii",
		A2:  "ii",
	},
	{
		A3B: "ijo",
		A3T: "ijo",
		A2:  "",
	},
	{
		A3B: "iku",
		A3T: "iku",
		A2:  "iu",
	},
	{
		A3B: "ile",
		A3T: "ile",
		A2:  "ie",
	},
	{
		A3B: "ilo",
		A3T: "ilo",
		A2:  "",
	},
	{
		A3B: "ina",
		A3T: "ina",
		A2:  "ia",
	},
	{
		A3B: "inc",
		A3T: "inc",
		A2:  "",
	},
	{
		A3B: "ind",
		A3T: "ind",
		A2:  "id",
	},
	{
		A3B: "ine",
		A3T: "ine",
		A2:  "",
	},
	{
		A3B: "inh",
		A3T: "inh",
		A2:  "",
	},
	{
		A3B: "ipk",
		A3T: "ipk",
		A2:  "ik",
	},
	{
		A3B: "ira",
		A3T: "ira",
		A2:  "",
	},
	{
		A3B: "iro",
		A3T: "iro",
		A2:  "",
	},
	{
		A3B: "ita",
		A3T: "ita",
		A2:  "it",
	},
	{
		A3B: "jav",
		A3T: "jav",
		A2:  "jv",
	},
	{
		A3B: "jbo",
		A3T: "jbo",
		A2:  "",
	},
	{
		A3B: "jpn",
		A3T: "jpn",
		A2:  "ja",
	},
	{
		A3B: "jpr",
		A3T: "jpr",
		A2:  "",
	},
	{
		A3B: "jrb",
		A3T: "jrb",
		A2:  "",
	},
	{
		A3B: "kaa",
		A3T: "kaa",
		A2:  "",
	},
	{
		A3B: "kab",
		A3T: "kab",
		A2:  "",
	},
	{
		A3B: "kac",
		A3T: "kac",
		A2:  "",
	},
	{
		A3B: "kal",
		A3T: "kal",
		A2:  "kl",
	},
	{
		A3B: "kam",
		A3T: "kam",
		A2:  "",
	},
	{
		A3B: "kan",
		A3T: "kan",
		A2:  "kn",
	},
	{
		A3B: "kar",
		A3T: "kar",
		A2:  "",
	},
	{
		A3B: "kas",
		A3T: "kas",
		A2:  "ks",
	},
	{
		A3B: "kau",
		A3T: "kau",
		A2:  "kr",
	},
	{
		A3B: "kaw",
		A3T: "kaw",
		A2:  "",
	},
	{
		A3B: "kaz",
		A3T: "kaz",
		A2:  "kk",
	},
	{
		A3B: "kbd",
		A3T: "kbd",
		A2:  "",
	},
	{
		A3B: "kha",
		A3T: "kha",
		A2:  "",
	},
	{
		A3B: "khi",
		A3T: "khi",
		A2:  "",
	},
	{
		A3B: "khm",
		A3T: "khm",
		A2:  "km",
	},
	{
		A3B: "kho",
		A3T: "kho",
		A2:  "",
	},
	{
		A3B: "kik",
		A3T: "kik",
		A2:  "ki",
	},
	{
		A3B: "kin",
		A3T: "kin",
		A2:  "rw",
	},
	{
		A3B: "kir",
		A3T: "kir",
		A2:  "ky",
	},
	{
		A3B: "kmb",
		A3T: "kmb",
		A2:  "",
	},
	{
		A3B: "kok",
		A3T: "kok",
		A2:  "",
	},
	{
		A3B: "kom",
		A3T: "kom",
		A2:  "kv",
	},
	{
		A3B: "kon",
		A3T: "kon",
		A2:  "kg",
	},
	{
		A3B: "kor",
		A3T: "kor",
		A2:  "ko",
	},
	{
		A3B: "kos",
		A3T: "kos",
		A2:  "",
	},
	{
		A3B: "kpe",
		A3T: "kpe",
		A2:  "",
	},
	{
		A3B: "krc",
		A3T: "krc",
		A2:  "",
	},
	{
		A3B: "krl",
		A3T: "krl",
		A2:  "",
	},
	{
		A3B: "kro",
		A3T: "kro",
		A2:  "",
	},
	{
		A3B: "kru",
		A3T: "kru",
		A2:  "",
	},
	{
		A3B: "kua",
		A3T: "kua",
		A2:  "kj",
	},
	{
		A3B: "kum",
		A3T: "kum",
		A2:  "",
	},
	{
		A3B: "kur",
		A3T: "kur",
		A2:  "ku",
	},
	{
		A3B: "kut",
		A3T: "kut",
		A2:  "",
	},
	{
		A3B: "lad",
		A3T: "lad",
		A2:  "",
	},
	{
		A3B: "lah",
		A3T: "lah",
		A2:  "",
	},
	{
		A3B: "lam",
		A3T: "lam",
		A2:  "",
	},
	{
		A3B: "lao",
		A3T: "lao",
		A2:  "lo",
	},
	{
		A3B: "lat",
		A3T: "lat",
		A2:  "la",
	},
	{
		A3B: "lav",
		A3T: "lav",
		A2:  "lv",
	},
	{
		A3B: "lez",
		A3T: "lez",
		A2:  "",
	},
	{
		A3B: "lim",
		A3T: "lim",
		A2:  "li",
	},
	{
		A3B: "lin",
		A3T: "lin",
		A2:  "ln",
	},
	{
		A3B: "lit",
		A3T: "lit",
		A2:  "lt",
	},
	{
		A3B: "lol",
		A3T: "lol",
		A2:  "",
	},
	{
		A3B: "loz",
		A3T: "loz",
		A2:  "",
	},
	{
		A3B: "ltz",
		A3T: "ltz",
		A2:  "lb",
	},
	{
		A3B: "lua",
		A3T: "lua",
		A2:  "",
	},
	{
		A3B: "lub",
		A3T: "lub",
		A2:  "lu",
	},
	{
		A3B: "lug",
		A3T: "lug",
		A2:  "lg",
	},
	{
		A3B: "lui",
		A3T: "lui",
		A2:  "",
	},
	{
		A3B: "lun",
		A3T: "lun",
		A2:  "",
	},
	{
		A3B: "luo",
		A3T: "luo",
		A2:  "",
	},
	{
		A3B: "lus",
		A3T: "lus",
		A2:  "",
	},
	{
		A3B: "mac",
		A3T: "mkd",
		A2:  "mk",
	},
	{
		A3B: "mad",
		A3T: "mad",
		A2:  "",
	},
	{
		A3B: "mag",
		A3T: "mag",
		A2:  "",
	},
	{
		A3B: "mah",
		A3T: "mah",
		A2:  "mh",
	},
	{
		A3B: "mai",
		A3T: "mai",
		A2:  "",
	},
	{
		A3B: "mak",
		A3T: "mak",
		A2:  "",
	},
	{
		A3B: "mal",
		A3T: "mal",
		A2:  "ml",
	},
	{
		A3B: "man",
		A3T: "man",
		A2:  "",
	},
	{
		A3B: "mao",
		A3T: "mri",
		A2:  "mi",
	},
	{
		A3B: "map",
		A3T: "map",
		A2:  "",
	},
	{
		A3B: "mar",
		A3T: "mar",
		A2:  "mr",
	},
	{
		A3B: "mas",
		A3T: "mas",
		A2:  "",
	},
	{
		A3B: "may",
		A3T: "msa",
		A2:  "ms",
	},
	{
		A3B: "mdf",
		A3T: "mdf",
		A2:  "",
	},
	{
		A3B: "mdr",
		A3T: "mdr",
		A2:  "",
	},
	{
		A3B: "men",
		A3T: "men",
		A2:  "",
	},
	{
		A3B: "mga",
		A3T: "mga",
		A2:  "",
	},
	{
		A3B: "mic",
		A3T: "mic",
		A2:  "",
	},
	{
		A3B: "min",
		A3T: "min",
		A2:  "",
	},
	{
		A3B: "mis",
		A3T: "mis",
		A2:  "",
	},
	{
		A3B: "mkh",
		A3T: "mkh",
		A2:  "",
	},
	{
		A3B: "mlg",
		A3T: "mlg",
		A2:  "mg",
	},
	{
		A3B: "mlt",
		A3T: "mlt",
		A2:  "mt",
	},
	{
		A3B: "mnc",
		A3T: "mnc",
		A2:  "",
	},
	{
		A3B: "mni",
		A3T: "mni",
		A2:  "",
	},
	{
		A3B: "mno",
		A3T: "mno",
		A2:  "",
	},
	{
		A3B: "moh",
		A3T: "moh",
		A2:  "",
	},
	{
		A3B: "mon",
		A3T: "mon",
		A2:  "mn",
	},
	{
		A3B: "mos",
		A3T: "mos",
		A2:  "",
	},
	{
		A3B: "mul",
		A3T: "mul",
		A2:  "",
	},
	{
		A3B: "mun",
		A3T: "mun",
		A2:  "",
	},
	{
		A3B: "mus",
		A3T: "mus",
		A2:  "",
	},
	{
		A3B: "mwl",
		A3T: "mwl",
		A2:  "",
	},
	{
		A3B: "mwr",
		A3T: "mwr",
		A2:  "",
	},
	{
		A3B: "myn",
		A3T: "myn",
		A2:  "",
	},
	{
		A3B: "myv",
		A3T: "myv",
		A2:  "",
	},
	{
		A3B: "nah",
		A3T: "nah",
		A2:  "",
	},
	{
		A3B: "nai",
		A3T: "nai",
		A2:  "",
	},
	{
		A3B: "nap",
		A3T: "nap",
		A2:  "",
	},
	{
		A3B: "nau",
		A3T: "nau",
		A2:  "na",
	},
	{
		A3B: "nav",
		A3T: "nav",
		A2:  "nv",
	},
	{
		A3B: "nbl",
		A3T: "nbl",
		A2:  "nr",
	},
	{
		A3B: "nde",
		A3T: "nde",
		A2:  "nd",
	},
	{
		A3B: "ndo",
		A3T: "ndo",
		A2:  "ng",
	},
	{
		A3B: "nds",
		A3T: "nds",
		A2:  "",
	},
	{
		A3B: "nep",
		A3T: "nep",
		A2:  "ne",
	},
	{
		A3B: "new",
		A3T: "new",
		A2:  "",
	},
	{
		A3B: "nia",
		A3T: "nia",
		A2:  "",
	},
	{
		A3B: "nic",
		A3T: "nic",
		A2:  "",
	},
	{
		A3B: "niu",
		A3T: "niu",
		A2:  "",
	},
	{
		A3B: "nno",
		A3T: "nno",
		A2:  "nn",
	},
	{
		A3B: "nob",
		A3T: "nob",
		A2:  "nb",
	},
	{
		A3B: "nog",
		A3T: "nog",
		A2:  "",
	},
	{
		A3B: "non",
		A3T: "non",
		A2:  "",
	},
	{
		A3B: "nor",
		A3T: "nor",
		A2:  "no",
	},
	{
		A3B: "nqo",
		A3T: "nqo",
		A2:  "",
	},
	{
		A3B: "nso",
		A3T: "nso",
		A2:  "",
	},
	{
		A3B: "nub",
		A3T: "nub",
		A2:  "",
	},
	{
		A3B: "nwc",
		A3T: "nwc",
		A2:  "",
	},
	{
		A3B: "nya",
		A3T: "nya",
		A2:  "ny",
	},
	{
		A3B: "nym",
		A3T: "nym",
		A2:  "",
	},
	{
		A3B: "nyn",
		A3T: "nyn",
		A2:  "",
	},
	{
		A3B: "nyo",
		A3T: "nyo",
		A2:  "",
	},
	{
		A3B: "nzi",
		A3T: "nzi",
		A2:  "",
	},
	{
		A3B: "oci",
		A3T: "oci",
		A2:  "oc",
	},
	{
		A3B: "oji",
		A3T: "oji",
		A2:  "oj",
	},
	{
		A3B: "ori",
		A3T: "ori",
		A2:  "or",
	},
	{
		A3B: "orm",
		A3T: "orm",
		A2:  "om",
	},
	{
		A3B: "osa",
		A3T: "osa",
		A2:  "",
	},
	{
		A3B: "oss",
		A3T: "oss",
		A2:  "os",
	},
	{
		A3B: "ota",
		A3T: "ota",
		A2:  "",
	},
	{
		A3B: "oto",
		A3T: "oto",
		A2:  "",
	},
	{
		A3B: "paa",
		A3T: "paa",
		A2:  "",
	},
	{
		A3B: "pag",
		A3T: "pag",
		A2:  "",
	},
	{
		A3B: "pal",
		A3T: "pal",
		A2:  "",
	},
	{
		A3B: "pam",
		A3T: "pam",
		A2:  "",
	},
	{
		A3B: "pan",
		A3T: "pan",
		A2:  "pa",
	},
	{
		A3B: "pap",
		A3T: "pap",
		A2:  "",
	},
	{
		A3B: "pau",
		A3T: "pau",
		A2:  "",
	},
	{
		A3B: "peo",
		A3T: "peo",
		A2:  "",
	},
	{
		A3B: "per",
		A3T: "fas",
		A2:  "fa",
	},
	{
		A3B: "phi",
		A3T: "phi",
		A2:  "",
	},
	{
		A3B: "phn",
		A3T: "phn",
		A2:  "",
	},
	{
		A3B: "pli",
		A3T: "pli",
		A2:  "pi",
	},
	{
		A3B: "pol",
		A3T: "pol",
		A2:  "pl",
	},
	{
		A3B: "pon",
		A3T: "pon",
		A2:  "",
	},
	{
		A3B: "por",
		A3T: "por",
		A2:  "pt",
	},
	{
		A3B: "pra",
		A3T: "pra",
		A2:  "",
	},
	{
		A3B: "pro",
		A3T: "pro",
		A2:  "",
	},
	{
		A3B: "pus",
		A3T: "pus",
		A2:  "ps",
	},
	{
		A3B: "qaa-qtz",
		A3T: "qaa-qtz",
		A2:  "",
	},
	{
		A3B: "que",
		A3T: "que",
		A2:  "qu",
	},
	{
		A3B: "raj",
		A3T: "raj",
		A2:  "",
	},
	{
		A3B: "rap",
		A3T: "rap",
		A2:  "",
	},
	{
		A3B: "rar",
		A3T: "rar",
		A2:  "",
	},
	{
		A3B: "roa",
		A3T: "roa",
		A2:  "",
	},
	{
		A3B: "roh",
		A3T: "roh",
		A2:  "rm",
	},
	{
		A3B: "rom",
		A3T: "rom",
		A2:  "",
	},
	{
		A3B: "rum",
		A3T: "ron",
		A2:  "ro",
	},
	{
		A3B: "run",
		A3T: "run",
		A2:  "rn",
	},
	{
		A3B: "rup",
		A3T: "rup",
		A2:  "",
	},
	{
		A3B: "rus",
		A3T: "rus",
		A2:  "ru",
	},
	{
		A3B: "sad",
		A3T: "sad",
		A2:  "",
	},
	{
		A3B: "sag",
		A3T: "sag",
		A2:  "sg",
	},
	{
		A3B: "sah",
		A3T: "sah",
		A2:  "",
	},
	{
		A3B: "sai",
		A3T: "sai",
		A2:  "",
	},
	{
		A3B: "sal",
		A3T: "sal",
		A2:  "",
	},
	{
		A3B: "sam",
		A3T: "sam",
		A2:  "",
	},
	{
		A3B: "san",
		A3T: "san",
		A2:  "sa",
	},
	{
		A3B: "sas",
		A3T: "sas",
		A2:  "",
	},
	{
		A3B: "sat",
		A3T: "sat",
		A2:  "",
	},
	{
		A3B: "scn",
		A3T: "scn",
		A2:  "",
	},
	{
		A3B: "sco",
		A3T: "sco",
		A2:  "",
	},
	{
		A3B: "sel",
		A3T: "sel",
		A2:  "",
	},
	{
		A3B: "sem",
		A3T: "sem",
		A2:  "",
	},
	{
		A3B: "sga",
		A3T: "sga",
		A2:  "",
	},
	{
		A3B: "sgn",
		A3T: "sgn",
		A2:  "",
	},
	{
		A3B: "shn",
		A3T: "shn",
		A2:  "",
	},
	{
		A3B: "sid",
		A3T: "sid",
		A2:  "",
	},
	{
		A3B: "sin",
		A3T: "sin",
		A2:  "si",
	},
	{
		A3B: "sio",
		A3T: "sio",
		A2:  "",
	},
	{
		A3B: "sit",
		A3T: "sit",
		A2:  "",
	},
	{
		A3B: "sla",
		A3T: "sla",
		A2:  "",
	},
	{
		A3B: "slo",
		A3T: "slk",
		A2:  "sk",
	},
	{
		A3B: "slv",
		A3T: "slv",
		A2:  "sl",
	},
	{
		A3B: "sma",
		A3T: "sma",
		A2:  "",
	},
	{
		A3B: "sme",
		A3T: "sme",
		A2:  "se",
	},
	{
		A3B: "smi",
		A3T: "smi",
		A2:  "",
	},
	{
		A3B: "smj",
		A3T: "smj",
		A2:  "",
	},
	{
		A3B: "smn",
		A3T: "smn",
		A2:  "",
	},
	{
		A3B: "smo",
		A3T: "smo",
		A2:  "sm",
	},
	{
		A3B: "sms",
		A3T: "sms",
		A2:  "",
	},
	{
		A3B: "sna",
		A3T: "sna",
		A2:  "sn",
	},
	{
		A3B: "snd",
		A3T: "snd",
		A2:  "sd",
	},
	{
		A3B: "snk",
		A3T: "snk",
		A2:  "",
	},
	{
		A3B: "sog",
		A3T: "sog",
		A2:  "",
	},
	{
		A3B: "som",
		A3T: "som",
		A2:  "so",
	},
	{
		A3B: "son",
		A3T: "son",
		A2:  "",
	},
	{
		A3B: "sot",
		A3T: "sot",
		A2:  "st",
	},
	{
		A3B: "spa",
		A3T: "spa",
		A2:  "es",
	},
	{
		A3B: "srd",
		A3T: "srd",
		A2:  "sc",
	},
	{
		A3B: "srn",
		A3T: "srn",
		A2:  "",
	},
	{
		A3B: "srp",
		A3T: "srp",
		A2:  "sr",
	},
	{
		A3B: "srr",
		A3T: "srr",
		A2:  "",
	},
	{
		A3B: "ssa",
		A3T: "ssa",
		A2:  "",
	},
	{
		A3B: "ssw",
		A3T: "ssw",
		A2:  "ss",
	},
	{
		A3B: "suk",
		A3T: "suk",
		A2:  "",
	},
	{
		A3B: "sun",
		A3T: "sun",
		A2:  "su",
	},
	{
		A3B: "sus",
		A3T: "sus",
		A2:  "",
	},
	{
		A3B: "sux",
		A3T: "sux",
		A2:  "",
	},
	{
		A3B: "swa",
		A3T: "swa",
		A2:  "sw",
	},
	{
		A3B: "swe",
		A3T: "swe",
		A2:  "sv",
	},
	{
		A3B: "syc",
		A3T: "syc",
		A2:  "",
	},
	{
		A3B: "syr",
		A3T: "syr",
		A2:  "",
	},
	{
		A3B: "tah",
		A3T: "tah",
		A2:  "ty",
	},
	{
		A3B: "tai",
		A3T: "tai",
		A2:  "",
	},
	{
		A3B: "tam",
		A3T: "tam",
		A2:  "ta",
	},
	{
		A3B: "tat",
		A3T: "tat",
		A2:  "tt",
	},
	{
		A3B: "tel",
		A3T: "tel",
		A2:  "te",
	},
	{
		A3B: "tem",
		A3T: "tem",
		A2:  "",
	},
	{
		A3B: "ter",
		A3T: "ter",
		A2:  "",
	},
	{
		A3B: "tet",
		A3T: "tet",
		A2:  "",
	},
	{
		A3B: "tgk",
		A3T: "tgk",
		A2:  "tg",
	},
	{
		A3B: "tgl",
		A3T: "tgl",
		A2:  "tl",
	},
	{
		A3B: "tha",
		A3T: "tha",
		A2:  "th",
	},
	{
		A3B: "tib",
		A3T: "bod",
		A2:  "bo",
	},
	{
		A3B: "tig",
		A3T: "tig",
		A2:  "",
	},
	{
		A3B: "tir",
		A3T: "tir",
		A2:  "ti",
	},
	{
		A3B: "tiv",
		A3T: "tiv",
		A2:  "",
	},
	{
		A3B: "tkl",
		A3T: "tkl",
		A2:  "",
	},
	{
		A3B: "tlh",
		A3T: "tlh",
		A2:  "",
	},
	{
		A3B: "tli",
		A3T: "tli",
		A2:  "",
	},
	{
		A3B: "tmh",
		A3T: "tmh",
		A2:  "",
	},
	{
		A3B: "tog",
		A3T: "tog",
		A2:  "",
	},
	{
		A3B: "ton",
		A3T: "ton",
		A2:  "to",
	},
	{
		A3B: "tpi",
		A3T: "tpi",
		A2:  "",
	},
	{
		A3B: "tsi",
		A3T: "tsi",
		A2:  "",
	},
	{
		A3B: "tsn",
		A3T: "tsn",
		A2:  "tn",
	},
	{
		A3B: "tso",
		A3T: "tso",
		A2:  "ts",
	},
	{
		A3B: "tuk",
		A3T: "tuk",
		A2:  "tk",
	},
	{
		A3B: "tum",
		A3T: "tum",
		A2:  "",
	},
	{
		A3B: "tup",
		A3T: "tup",
		A2:  "",
	},
	{
		A3B: "tur",
		A3T: "tur",
		A2:  "tr",
	},
	{
		A3B: "tut",
		A3T: "tut",
		A2:  "",
	},
	{
		A3B: "tvl",
		A3T: "tvl",
		A2:  "",
	},
	{
		A3B: "twi",
		A3T: "twi",
		A2:  "tw",
	},
	{
		A3B: "tyv",
		A3T: "tyv",
		A2:  "",
	},
	{
		A3B: "udm",
		A3T: "udm",
		A2:  "",
	},
	{
		A3B: "uga",
		A3T: "uga",
		A2:  "",
	},
	{
		A3B: "uig",
		A3T: "uig",
		A2:  "ug",
	},
	{
		A3B: "ukr",
		A3T: "ukr",
		A2:  "uk",
	},
	{
		A3B: "umb",
		A3T: "umb",
		A2:  "",
	},
	{
		A3B: "und",
		A3T: "und",
		A2:  "",
	},
	{
		A3B: "urd",
		A3T: "urd",
		A2:  "ur",
	},
	{
		A3B: "uzb",
		A3T: "uzb",
		A2:  "uz",
	},
	{
		A3B: "vai",
		A3T: "vai",
		A2:  "",
	},
	{
		A3B: "ven",
		A3T: "ven",
		A2:  "ve",
	},
	{
		A3B: "vie",
		A3T: "vie",
		A2:  "vi",
	},
	{
		A3B: "vol",
		A3T: "vol",
		A2:  "vo",
	},
	{
		A3B: "vot",
		A3T: "vot",
		A2:  "",
	},
	{
		A3B: "wak",
		A3T: "wak",
		A2:  "",
	},
	{
		A3B: "wal",
		A3T: "wal",
		A2:  "",
	},
	{
		A3B: "war",
		A3T: "war",
		A2:  "",
	},
	{
		A3B: "was",
		A3T: "was",
		A2:  "",
	},
	{
		A3B: "wel",
		A3T: "cym",
		A2:  "cy",
	},
	{
		A3B: "wen",
		A3T: "wen",
		A2:  "",
	},
	{
		A3B: "wln",
		A3T: "wln",
		A2:  "wa",
	},
	{
		A3B: "wol",
		A3T: "wol",
		A2:  "wo",
	},
	{
		A3B: "xal",
		A3T: "xal",
		A2:  "",
	},
	{
		A3B: "xho",
		A3T: "xho",
		A2:  "xh",
	},
	{
		A3B: "yao",
		A3T: "yao",
		A2:  "",
	},
	{
		A3B: "yap",
		A3T: "yap",
		A2:  "",
	},
	{
		A3B: "yid",
		A3T: "yid",
		A2:  "yi",
	},
	{
		A3B: "yor",
		A3T: "yor",
		A2:  "yo",
	},
	{
		A3B: "ypk",
		A3T: "ypk",
		A2:  "",
	},
	{
		A3B: "zap",
		A3T: "zap",
		A2:  "",
	},
	{
		A3B: "zbl",
		A3T: "zbl",
		A2:  "",
	},
	{
		A3B: "zen",
		A3T: "zen",
		A2:  "",
	},
	{
		A3B: "zgh",
		A3T: "zgh",
		A2:  "",
	},
	{
		A3B: "zha",
		A3T: "zha",
		A2:  "za",
	},
	{
		A3B: "znd",
		A3T: "znd",
		A2:  "",
	},
	{
		A3B: "zul",
		A3T: "zul",
		A2:  "zu",
	},
	{
		A3B: "zun",
		A3T: "zun",
		A2:  "",
	},
	{
		A3B: "zxx",
		A3T: "zxx",
		A2:  "",
	},
	{
		A3B: "zza",
		A3T: "zza",
		A2:  "",
	},
}
