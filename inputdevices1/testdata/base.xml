<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE xkbConfigRegistry SYSTEM "xkb.dtd">
<xkbConfigRegistry version="1.1">
  <modelList>
    <model>
      <configItem>
        <name>pc101</name>
        <description>Generic 101-key PC</description>
        <vendor>Generic</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>pc102</name>
        <description>Generic 102-key (Intl) PC</description>
        <vendor>Generic</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>pc104</name>
        <description>Generic 104-key PC</description>
        <vendor>Generic</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>pc105</name>
        <description>Generic 105-key (Intl) PC</description>
        <vendor>Generic</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>dell101</name>
        <description>Dell 101-key PC</description>
        <vendor>Dell</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>latitude</name>
        <description>Dell Latitude series laptop</description>
        <vendor>Dell</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>dellm65</name>
        <description>Dell Precision M65</description>
        <vendor>Dell</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>everex</name>
        <description>Everex STEPnote</description>
        <vendor>Everex</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>flexpro</name>
        <description>Keytronic FlexPro</description>
        <vendor>Keytronic</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>microsoft</name>
        <description>Microsoft Natural</description>
        <vendor>Microsoft Inc.</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>omnikey101</name>
        <description>Northgate OmniKey 101</description>
        <vendor>Northgate</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>winbook</name>
        <description>Winbook Model XP5</description>
        <vendor>Generic</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>pc98</name>
        <description>PC-98xx Series</description>
        <vendor>Generic</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>a4techKB21</name>
        <description>A4Tech KB-21</description>
        <vendor>A4Tech</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>a4techKBS8</name>
        <description>A4Tech KBS-8</description>
        <vendor>A4Tech</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>a4_rfkb23</name>
        <description>A4Tech Wireless Desktop RFKB-23</description>
        <vendor>A4Tech</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>airkey</name>
        <description>Acer AirKey V</description>
        <vendor>Acer</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>azonaRF2300</name>
        <description>Azona RF2300 wireless Internet Keyboard</description>
        <vendor>Azona</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>scorpius</name>
        <description>Advance Scorpius KI</description>
        <vendor>Scorpius</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>brother</name>
        <description>Brother Internet Keyboard</description>
        <vendor>Brother</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>btc5113rf</name>
        <description>BTC 5113RF Multimedia</description>
        <vendor>BTC</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>btc5126t</name>
        <description>BTC 5126T</description>
        <vendor>BTC</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>btc6301urf</name>
        <description>BTC 6301URF</description>
        <vendor>BTC</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>btc9000</name>
        <description>BTC 9000</description>
        <vendor>BTC</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>btc9000a</name>
        <description>BTC 9000A</description>
        <vendor>BTC</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>btc9001ah</name>
        <description>BTC 9001AH</description>
        <vendor>BTC</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>btc5090</name>
        <description>BTC 5090</description>
        <vendor>BTC</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>btc9019u</name>
        <description>BTC 9019U</description>
        <vendor>BTC</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>btc9116u</name>
        <description>BTC 9116U Mini Wireless Internet and Gaming</description>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>cherryblue</name>
        <description>Cherry Blue Line CyBo@rd</description>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>cherryblueb</name>
        <description>Cherry CyMotion Master XPress</description>
        <vendor>Cherry</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>cherrybluea</name>
        <description>Cherry Blue Line CyBo@rd (alternate option)</description>
        <vendor>Cherry</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>cherrycyboard</name>
        <description>Cherry CyBo@rd USB-Hub</description>
        <vendor>Cherry</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>cherrycmexpert</name>
        <description>Cherry CyMotion Expert</description>
        <vendor>Cherry</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>cherrybunlim</name>
        <description>Cherry B.UNLIMITED</description>
        <vendor>Cherry</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>chicony</name>
        <description>Chicony Internet Keyboard</description>
        <vendor>Chicony</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>chicony0108</name>
        <description>Chicony KU-0108</description>
        <vendor>Chicony</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>chicony0420</name>
        <description>Chicony KU-0420</description>
        <vendor>Chicony</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>chicony9885</name>
        <description>Chicony KB-9885</description>
        <vendor>Chicony</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>compaqeak8</name>
        <description>Compaq Easy Access Keyboard</description>
        <vendor>Compaq</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>compaqik7</name>
        <description>Compaq Internet Keyboard (7 keys)</description>
        <vendor>Compaq</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>compaqik13</name>
        <description>Compaq Internet Keyboard (13 keys)</description>
        <vendor>Compaq</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>compaqik18</name>
        <description>Compaq Internet Keyboard (18 keys)</description>
        <vendor>Compaq</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>cymotionlinux</name>
        <description>Cherry CyMotion Master Linux</description>
        <vendor>Cherry</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>armada</name>
        <description>Laptop/notebook Compaq (eg. Armada) Laptop Keyboard</description>
        <vendor>Compaq</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>presario</name>
        <description>Laptop/notebook Compaq (eg. Presario) Internet Keyboard</description>
        <vendor>Compaq</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>ipaq</name>
        <description>Compaq iPaq Keyboard</description>
        <vendor>Compaq</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>dell</name>
        <description>Dell</description>
        <vendor>Dell</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>dellsk8125</name>
        <description>Dell SK-8125</description>
        <vendor>Dell</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>dellsk8135</name>
        <description>Dell SK-8135</description>
        <vendor>Dell</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>dellusbmm</name>
        <description>Dell USB Multimedia Keyboard</description>
        <vendor>Dell</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>inspiron</name>
        <description>Dell Laptop/notebook Inspiron 6xxx/8xxx</description>
        <vendor>Dell</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>precision_m</name>
        <description>Dell Laptop/notebook Precision M series</description>
        <vendor>Dell</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>dexxa</name>
        <description>Dexxa Wireless Desktop Keyboard</description>
        <vendor>Dexxa</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>diamond</name>
        <description>Diamond 9801 / 9802 series</description>
        <vendor>Diamond</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>dtk2000</name>
        <description>DTK2000</description>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>ennyah_dkb1008</name>
        <description>Ennyah DKB-1008</description>
        <vendor>Ennyah</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>fscaa1667g</name>
        <description>Fujitsu-Siemens Computers AMILO laptop</description>
        <vendor>Fujitsu-Siemens</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>genius</name>
        <description>Genius Comfy KB-16M / Genius MM Keyboard KWD-910</description>
        <vendor>Genius</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>geniuscomfy</name>
        <description>Genius Comfy KB-12e</description>
        <vendor>Genius</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>geniuscomfy2</name>
        <description>Genius Comfy KB-21e-Scroll</description>
        <vendor>Genius</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>geniuskb19e</name>
        <description>Genius KB-19e NB</description>
        <vendor>Genius</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>geniuskkb2050hs</name>
        <description>Genius KKB-2050HS</description>
        <vendor>Genius</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>gyration</name>
        <description>Gyration</description>
        <vendor>Gyration</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>htcdream</name>
        <description>HTC Dream</description>
        <vendor>HTC</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>kinesis</name>
        <description>Kinesis</description>
        <vendor>Kinesis</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>logitech_base</name>
        <description>Logitech Generic Keyboard</description>
        <vendor>Logitech</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>logitech_g15</name>
        <description>Logitech G15 extra keys via G15daemon</description>
        <vendor>Logitech</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>hpi6</name>
        <description>Hewlett-Packard Internet Keyboard</description>
        <vendor>Hewlett-Packard</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>hp250x</name>
        <description>Hewlett-Packard SK-250x Multimedia Keyboard</description>
        <vendor>Hewlett-Packard</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>hpxe3gc</name>
        <description>Hewlett-Packard Omnibook XE3 GC</description>
        <vendor>Hewlett-Packard</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>hpxe3gf</name>
        <description>Hewlett-Packard Omnibook XE3 GF</description>
        <vendor>Hewlett-Packard</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>hpxt1000</name>
        <description>Hewlett-Packard Omnibook XT1000</description>
        <vendor>Hewlett-Packard</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>hpdv5</name>
        <description>Hewlett-Packard Pavilion dv5</description>
        <vendor>Hewlett-Packard</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>hpzt11xx</name>
        <description>Hewlett-Packard Pavilion ZT11xx</description>
        <vendor>Hewlett-Packard</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>hp500fa</name>
        <description>Hewlett-Packard Omnibook 500 FA</description>
        <vendor>Hewlett-Packard</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>hp5xx</name>
        <description>Hewlett-Packard Omnibook 5xx</description>
        <vendor>Hewlett-Packard</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>hpnx9020</name>
        <description>Hewlett-Packard nx9020</description>
        <vendor>Hewlett-Packard</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>hp6000</name>
        <description>Hewlett-Packard Omnibook 6000/6100</description>
        <vendor>Hewlett-Packard</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>honeywell_euroboard</name>
        <description>Honeywell Euroboard</description>
        <vendor>Hewlett-Packard</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>hpmini110</name>
        <description>Hewlett-Packard Mini 110 Notebook</description>
        <vendor>Hewlett-Packard</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>rapidaccess</name>
        <description>IBM Rapid Access</description>
        <vendor>Lenovo (previously IBM)</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>rapidaccess2</name>
        <description>IBM Rapid Access II</description>
        <vendor>Lenovo (previously IBM)</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>thinkpad</name>
        <description>IBM ThinkPad 560Z/600/600E/A22E</description>
        <vendor>Lenovo (previously IBM)</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>thinkpad60</name>
        <description>IBM ThinkPad R60/T60/R61/T61</description>
        <vendor>Lenovo (previously IBM)</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>thinkpadz60</name>
        <description>IBM ThinkPad Z60m/Z60t/Z61m/Z61t</description>
        <vendor>Lenovo (previously IBM)</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>ibm_spacesaver</name>
        <description>IBM Space Saver</description>
        <vendor>Lenovo (previously IBM)</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>logiaccess</name>
        <description>Logitech Access Keyboard</description>
        <vendor>Logitech</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>logiclx300</name>
        <description>Logitech Cordless Desktop LX-300</description>
        <vendor>Logitech</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>logii350</name>
        <description>Logitech Internet 350 Keyboard</description>
        <vendor>Logitech</vendor>
        <hwList> <hwId>046d:c313</hwId></hwList>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>logimel</name>
        <description>Logitech Media Elite Keyboard</description>
        <vendor>Logitech</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>logicd</name>
        <description>Logitech Cordless Desktop</description>
        <vendor>Logitech</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>logicd_it</name>
        <description>Logitech Cordless Desktop iTouch</description>
        <vendor>Logitech</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>logicd_nav</name>
        <description>Logitech Cordless Desktop Navigator</description>
        <vendor>Logitech</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>logicd_opt</name>
        <description>Logitech Cordless Desktop Optical</description>
        <vendor>Logitech</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>logicda</name>
        <description>Logitech Cordless Desktop (alternate option)</description>
        <vendor>Logitech</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>logicdpa2</name>
        <description>Logitech Cordless Desktop Pro (alternate option 2)</description>
        <vendor>Logitech</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>logicfn</name>
        <description>Logitech Cordless Freedom/Desktop Navigator</description>
        <vendor>Logitech</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>logicdn</name>
        <description>Logitech Cordless Desktop Navigator</description>
        <vendor>Logitech</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>logiitc</name>
        <description>Logitech iTouch Cordless Keyboard (model Y-RB6)</description>
        <vendor>Logitech</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>logiik</name>
        <description>Logitech Internet Keyboard</description>
        <vendor>Logitech</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>itouch</name>
        <description>Logitech iTouch</description>
        <vendor>Logitech</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>logicink</name>
        <description>Logitech Internet Navigator Keyboard</description>
        <vendor>Logitech</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>logiex110</name>
        <description>Logitech Cordless Desktop EX110</description>
        <vendor>Logitech</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>logiinkse</name>
        <description>Logitech iTouch Internet Navigator Keyboard SE</description>
        <vendor>Logitech</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>logiinkseusb</name>
        <description>Logitech iTouch Internet Navigator Keyboard SE (USB)</description>
        <vendor>Logitech</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>logiultrax</name>
        <description>Logitech Ultra-X Keyboard</description>
        <vendor>Logitech</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>logiultraxc</name>
        <description>Logitech Ultra-X Cordless Media Desktop Keyboard</description>
        <vendor>Logitech</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>logidinovo</name>
        <description>Logitech diNovo Keyboard</description>
        <vendor>Logitech</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>logidinovoedge</name>
        <description>Logitech diNovo Edge Keyboard</description>
        <vendor>Logitech</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>mx1998</name>
        <description>Memorex MX1998</description>
        <vendor>Memorex</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>mx2500</name>
        <description>Memorex MX2500 EZ-Access Keyboard</description>
        <vendor>Memorex</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>mx2750</name>
        <description>Memorex MX2750</description>
        <vendor>Memorex</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>microsoft4000</name>
        <description>Microsoft Natural Ergonomic Keyboard 4000</description>
        <vendor>Microsoft Inc.</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>microsoft7000</name>
        <description>Microsoft Natural Wireless Ergonomic Keyboard 7000</description>
        <vendor>Microsoft Inc.</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>microsoftinet</name>
        <description>Microsoft Internet Keyboard</description>
        <vendor>Microsoft Inc.</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>microsoftpro</name>
        <description>Microsoft Natural Keyboard Pro / Microsoft Internet Keyboard Pro</description>
        <vendor>Microsoft Inc.</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>microsoftprousb</name>
        <description>Microsoft Natural Keyboard Pro USB / Microsoft Internet Keyboard Pro</description>
        <vendor>Microsoft Inc.</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>microsoftprooem</name>
        <description>Microsoft Natural Keyboard Pro OEM</description>
        <vendor>Microsoft Inc.</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>vsonku306</name>
        <description>ViewSonic KU-306 Internet Keyboard</description>
        <vendor>ViewSonic</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>microsoftprose</name>
        <description>Microsoft Internet Keyboard Pro, Swedish</description>
        <vendor>Microsoft Inc.</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>microsoftoffice</name>
        <description>Microsoft Office Keyboard</description>
        <vendor>Microsoft Inc.</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>microsoftmult</name>
        <description>Microsoft Wireless Multimedia Keyboard 1.0A</description>
        <vendor>Microsoft Inc.</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>microsoftelite</name>
        <description>Microsoft Natural Keyboard Elite</description>
        <vendor>Microsoft Inc.</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>microsoftccurve2k</name>
        <description>Microsoft Comfort Curve Keyboard 2000</description>
        <vendor>Microsoft Inc.</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>oretec</name>
        <description>Ortek MCK-800 MM/Internet keyboard</description>
        <vendor>Ortek</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>propeller</name>
        <description>Propeller Voyager (KTEZ-1000)</description>
        <vendor>KeyTronic</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>qtronix</name>
        <description>QTronix Scorpius 98N+</description>
        <vendor>QTronix</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>samsung4500</name>
        <description>Samsung SDM 4500P</description>
        <vendor>Samsung</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>samsung4510</name>
        <description>Samsung SDM 4510P</description>
        <vendor>Samsung</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>sanwaskbkg3</name>
        <description>Sanwa Supply SKB-KG3</description>
        <vendor>Sanwa Supply Inc.</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>sk1300</name>
        <description>SK-1300</description>
        <vendor>NEC</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>sk2500</name>
        <description>SK-2500</description>
        <vendor>NEC</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>sk6200</name>
        <description>SK-6200</description>
        <vendor>NEC</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>sk7100</name>
        <description>SK-7100</description>
        <vendor>NEC</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>sp_inet</name>
        <description>Super Power Multimedia Keyboard</description>
        <vendor>Generic</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>sven</name>
        <description>SVEN Ergonomic 2500</description>
        <vendor>SVEN</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>sven303</name>
        <description>SVEN Slim 303</description>
        <vendor>SVEN</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>symplon</name>
        <description>Symplon PaceBook (tablet PC)</description>
        <vendor>Symplon</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>toshiba_s3000</name>
        <description>Toshiba Satellite S3000</description>
        <vendor>Toshiba</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>trust</name>
        <description>Trust Wireless Keyboard Classic</description>
        <vendor>Trust</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>trustda</name>
        <description>Trust Direct Access Keyboard</description>
        <vendor>Trust</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>trust_slimline</name>
        <description>Trust Slimline</description>
        <vendor>Trust</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>tm2020</name>
        <description>TypeMatrix EZ-Reach 2020</description>
        <vendor>TypeMatrix</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>tm2030PS2</name>
        <description>TypeMatrix EZ-Reach 2030 PS2</description>
        <vendor>TypeMatrix</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>tm2030USB</name>
        <description>TypeMatrix EZ-Reach 2030 USB</description>
        <vendor>TypeMatrix</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>tm2030USB-102</name>
        <description>TypeMatrix EZ-Reach 2030 USB (102/105:EU mode)</description>
        <vendor>TypeMatrix</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>tm2030USB-106</name>
        <description>TypeMatrix EZ-Reach 2030 USB (106:JP mode)</description>
        <vendor>TypeMatrix</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>yahoo</name>
        <description>Yahoo! Internet Keyboard</description>
        <vendor>Yahoo!</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>macbook78</name>
        <description>MacBook/MacBook Pro</description>
        <vendor>Apple</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>macbook79</name>
        <description>MacBook/MacBook Pro (Intl)</description>
        <vendor>Apple</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>macintosh</name>
        <description>Macintosh</description>
        <vendor>Apple</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>macintosh_old</name>
        <description>Macintosh Old</description>
        <vendor>Apple</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>macintosh_hhk</name>
        <description>Happy Hacking Keyboard for Mac</description>
        <vendor>Fujitsu</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>acer_c300</name>
        <description>Acer C300</description>
        <vendor>Acer</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>acer_ferrari4k</name>
        <description>Acer Ferrari 4000</description>
        <vendor>Acer</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>acer_laptop</name>
        <description>Acer Laptop</description>
        <vendor>Acer</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>asus_laptop</name>
        <description>Asus Laptop</description>
        <vendor>Asus</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>apple</name>
        <description>Apple</description>
        <vendor>Apple</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>apple_laptop</name>
        <description>Apple Laptop</description>
        <vendor>Apple</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>applealu_ansi</name>
        <description>Apple Aluminium Keyboard (ANSI)</description>
        <vendor>Apple</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>applealu_iso</name>
        <description>Apple Aluminium Keyboard (ISO)</description>
        <vendor>Apple</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>applealu_jis</name>
        <description>Apple Aluminium Keyboard (JIS)</description>
        <vendor>Apple</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>silvercrest</name>
        <description>SILVERCREST Multimedia Wireless Keyboard</description>
        <vendor>Silvercrest</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>emachines</name>
        <description>Laptop/notebook eMachines m68xx</description>
        <vendor>eMachines</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>benqx</name>
        <description>BenQ X-Touch</description>
        <vendor>BenQ</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>benqx730</name>
        <description>BenQ X-Touch 730</description>
        <vendor>BenQ</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>benqx800</name>
        <description>BenQ X-Touch 800</description>
        <vendor>BenQ</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>hhk</name>
        <description>Happy Hacking Keyboard</description>
        <vendor>Fujitsu</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>classmate</name>
        <description>Classmate PC</description>
        <vendor>Intel</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>olpc</name>
        <description>OLPC</description>
        <vendor>OLPC</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>sun_type7_usb</name>
        <description>Sun Type 7 USB</description>
        <vendor>Sun Microsystems</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>sun_type7_euro_usb</name>
        <description>Sun Type 7 USB (European layout)</description>
        <vendor>Sun Microsystems</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>sun_type7_unix_usb</name>
        <description>Sun Type 7 USB (Unix layout)</description>
        <vendor>Sun Microsystems</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>sun_type7_jp_usb</name>
        <description>Sun Type 7 USB (Japanese layout) / Japanese 106-key</description>
        <vendor>Sun Microsystems</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>sun_type6_usb</name>
        <description>Sun Type 6/7 USB</description>
        <vendor>Sun Microsystems</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>sun_type6_euro_usb</name>
        <description>Sun Type 6/7 USB (European layout)</description>
        <vendor>Sun Microsystems</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>sun_type6_unix_usb</name>
        <description>Sun Type 6 USB (Unix layout)</description>
        <vendor>Sun Microsystems</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>sun_type6_jp_usb</name>
        <description>Sun Type 6 USB (Japanese layout)</description>
        <vendor>Sun Microsystems</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>sun_type6_jp</name>
        <description>Sun Type 6 (Japanese layout)</description>
        <vendor>Sun Microsystems</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>targa_v811</name>
        <description>Targa Visionary 811</description>
        <vendor>Targa</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>unitekkb1925</name>
        <description>Unitek KB-1925</description>
        <vendor>Unitek Group</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>compalfl90</name>
        <description>FL90</description>
        <vendor>Compal Electronics Inc.</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
        <name>creativedw7000</name>
        <description>Creative Desktop Wireless 7000</description>
        <vendor>Creative</vendor>
      </configItem>
    </model>
    <model>
      <configItem>
       <name>htcdream</name>
       <description>Htc Dream phone</description>
       <vendor>htc</vendor>
     </configItem>
    </model>
    <model>
      <configItem>
       <name>teck227</name>
       <description>Truly Ergonomic Computer Keyboard Model 227 (Wide Alt keys)</description>
       <vendor>Megawin Technology</vendor>
     </configItem>
    </model>
    <model>
      <configItem>
       <name>teck229</name>
       <description>Truly Ergonomic Computer Keyboard Model 229 (Standard sized Alt keys, additional Super and Menu key)</description>
       <vendor>Megawin Technology</vendor>
     </configItem>
    </model>
  </modelList>
  <layoutList>
    <layout>
      <configItem>
        <name>us</name>
        
        <shortDescription>en</shortDescription>
        <description>English (US)</description>
        <languageList>
          <iso639Id>eng</iso639Id>
        </languageList>
      </configItem>
      <variantList>
        <variant>
          <configItem>
            <name>chr</name>
            
            <shortDescription>chr</shortDescription>
            <description>Cherokee</description>
            <languageList>
              <iso639Id>chr</iso639Id>
            </languageList>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>euro</name>
            <description>English (US, with euro on 5)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>intl</name>
            <description>English (US, international with dead keys)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>alt-intl</name>
            <description>English (US, alternative international)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>colemak</name>
            <description>English (Colemak)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>dvorak</name>
            <description>English (Dvorak)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>dvorak-intl</name>
            <description>English (Dvorak, international with dead keys)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>dvorak-alt-intl</name>
            <description>English (Dvorak alternative international no dead keys)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>dvorak-l</name>
            <description>English (left handed Dvorak)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>dvorak-r</name>
            <description>English (right handed Dvorak)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>dvorak-classic</name>
            <description>English (classic Dvorak)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>dvp</name>
            <description>English (Programmer Dvorak)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>rus</name>
            
            <shortDescription>ru</shortDescription>
            <description>Russian (US, phonetic)</description>
            <languageList>
              <iso639Id>rus</iso639Id>
            </languageList>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>mac</name>
            <description>English (Macintosh)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>altgr-intl</name>
            <description>English (international AltGr dead keys)</description>
            <languageList>
              <iso639Id>eng</iso639Id>
              <iso639Id>fra</iso639Id>
              <iso639Id>ger</iso639Id>
            </languageList>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>olpc2</name>
            <description>English (the divide/multiply keys toggle the layout)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>hbs</name>
            <description>Serbo-Croatian (US)</description>
            <languageList>
              <iso639Id>eng</iso639Id>
              <iso639Id>bos</iso639Id>
              <iso639Id>hbs</iso639Id>
              <iso639Id>hrv</iso639Id>
              <iso639Id>srp</iso639Id>
            </languageList>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>workman</name>
            <description>English (Workman)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>workman-intl</name>
            <description>English (Workman, international with dead keys)</description>
           </configItem>
         </variant>
      </variantList>
    </layout>
    <layout>
      <configItem>
        <name>af</name>
        
        <shortDescription>fa</shortDescription>
        <description>Afghani</description>
      </configItem>
      <variantList>
        <variant>
          <configItem>
            <name>ps</name>
            
            <shortDescription>ps</shortDescription>
            <description>Pashto</description>
            <languageList>
              <iso639Id>pus</iso639Id>
            </languageList>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>uz</name>
            
            <shortDescription>uz</shortDescription>
            <description>Uzbek (Afghanistan)</description>
            <languageList>
              <iso639Id>uzb</iso639Id>
            </languageList>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>olpc-ps</name>
            
            <shortDescription>ps</shortDescription>
            <description>Pashto (Afghanistan, OLPC)</description>
            <languageList>
              <iso639Id>pus</iso639Id>
            </languageList>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>fa-olpc</name>
            
            <shortDescription>fa</shortDescription>
            <description>Persian (Afghanistan, Dari OLPC)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>uz-olpc</name>
            
            <shortDescription>uz</shortDescription>
            <description>Uzbek (Afghanistan, OLPC)</description>
            <languageList>
              <iso639Id>uzb</iso639Id>
            </languageList>
          </configItem>
        </variant>
      </variantList>
    </layout>
    <layout>
      <configItem>
        <name>ara</name>
        
        <shortDescription>ar</shortDescription>
        <description>Arabic</description>
        <countryList>
          <iso3166Id>AE</iso3166Id>
          <iso3166Id>BH</iso3166Id>
          <iso3166Id>DZ</iso3166Id>
          <iso3166Id>EG</iso3166Id>
          <iso3166Id>EH</iso3166Id>
          <iso3166Id>JO</iso3166Id>
          <iso3166Id>KW</iso3166Id>
          <iso3166Id>LB</iso3166Id>
          <iso3166Id>LY</iso3166Id>
          <iso3166Id>MA</iso3166Id>
          <iso3166Id>MR</iso3166Id>
          <iso3166Id>OM</iso3166Id>
          <iso3166Id>PS</iso3166Id>
          <iso3166Id>QA</iso3166Id>
          <iso3166Id>SA</iso3166Id>
          <iso3166Id>SD</iso3166Id>
          <iso3166Id>SY</iso3166Id>
          <iso3166Id>TN</iso3166Id>
          <iso3166Id>YE</iso3166Id>
        </countryList>
        <languageList>
          <iso639Id>ara</iso639Id>
        </languageList>
      </configItem>
      <variantList>
        <variant>
          <configItem>
            <name>azerty</name>
            <description>Arabic (azerty)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>azerty_digits</name>
            <description>Arabic (azerty/digits)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>digits</name>
            <description>Arabic (digits)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>qwerty</name>
            <description>Arabic (qwerty)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>qwerty_digits</name>
            <description>Arabic (qwerty/digits)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>buckwalter</name>
            <description>Arabic (Buckwalter)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>mac</name>
            <description>Arabic (Macintosh)</description>
          </configItem>
        </variant>
      </variantList>
    </layout>
    <layout>
      <configItem>
        <name>al</name>
        
        <shortDescription>sq</shortDescription>
        <description>Albanian</description>
        <languageList>
          <iso639Id>alb</iso639Id>
        </languageList>
      </configItem>
      <variantList>
        <variant>
          <configItem>
            <name>plisi-d1</name>
            <description>Albanian (Plisi D1)</description>
          </configItem>
        </variant>
      </variantList>
    </layout>
    <layout>
      <configItem>
        <name>am</name>
        
        <shortDescription>hy</shortDescription>
        <description>Armenian</description>
        <languageList>
          <iso639Id>hye</iso639Id>
        </languageList>
      </configItem>
      <variantList>
        <variant>
          <configItem>
            <name>phonetic</name>
            <description>Armenian (phonetic)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>phonetic-alt</name>
            <description>Armenian (alternative phonetic)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>eastern</name>
            <description>Armenian (eastern)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>western</name>
            <description>Armenian (western)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>eastern-alt</name>
            <description>Armenian (alternative eastern)</description>
          </configItem>
        </variant>
      </variantList>
    </layout>
    <layout>
      <configItem>
        <name>at</name>
        
        <shortDescription>de</shortDescription>
        <description>German (Austria)</description>
        <languageList>
          <iso639Id>ger</iso639Id>
        </languageList>
      </configItem>
      <variantList>
        <variant>
          <configItem>
            <name>nodeadkeys</name>
            <description>German (Austria, eliminate dead keys)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>sundeadkeys</name>
            <description>German (Austria, Sun dead keys)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>mac</name>
            <description>German (Austria, Macintosh)</description>
          </configItem>
        </variant>
      </variantList>
    </layout>
    <layout>
      <configItem>
        <name>az</name>
        
        <shortDescription>az</shortDescription>
        <description>Azerbaijani</description>
        <languageList>
          <iso639Id>aze</iso639Id>
        </languageList>
      </configItem>
      <variantList>
        <variant>
          <configItem>
            <name>cyrillic</name>
            <description>Azerbaijani (Cyrillic)</description>
          </configItem>
        </variant>
      </variantList>
    </layout>
    <layout>
      <configItem>
        <name>by</name>
        
        <shortDescription>by</shortDescription>
        <description>Belarusian</description>
        <languageList>
          <iso639Id>bel</iso639Id>
        </languageList>
      </configItem>
      <variantList>
        <variant>
          <configItem>
            <name>legacy</name>
            <description>Belarusian (legacy)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>latin</name>
            <description>Belarusian (Latin)</description>
          </configItem>
        </variant>
      </variantList>
    </layout>
    <layout>
      <configItem>
        <name>be</name>
        
        <shortDescription>be</shortDescription>
        <description>Belgian</description>
        <languageList>
          <iso639Id>ger</iso639Id>
          <iso639Id>nld</iso639Id>
          <iso639Id>fra</iso639Id>
        </languageList>
      </configItem>
      <variantList>
        <variant>
          <configItem>
            <name>oss</name>
            <description>Belgian (alternative)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>oss_latin9</name>
            <description>Belgian (alternative, Latin-9 only)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>oss_sundeadkeys</name>
            <description>Belgian (alternative, Sun dead keys)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>iso-alternate</name>
            <description>Belgian (ISO alternate)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>nodeadkeys</name>
            <description>Belgian (eliminate dead keys)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>sundeadkeys</name>
            <description>Belgian (Sun dead keys)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>wang</name>
            <description>Belgian (Wang model 724 azerty)</description>
          </configItem>
        </variant>
      </variantList>
    </layout>
    <layout>
      <configItem>
        <name>bd</name>
        
        <shortDescription>bn</shortDescription>
        <description>Bangla</description>
        <languageList>
          <iso639Id>ben</iso639Id>
          
          <iso639Id>sat</iso639Id>
        </languageList>
      </configItem>
      <variantList>
        <variant>
          <configItem>
            <name>probhat</name>
            <description>Bangla (Probhat)</description>
          </configItem>
        </variant>
      </variantList>
    </layout>
    <layout>
      <configItem>
        <name>in</name>
        
        <shortDescription>in</shortDescription>
        <description>Indian</description>
      </configItem>
      <variantList>
        <variant>
          <configItem>
            <name>ben</name>
            
            <shortDescription>bn</shortDescription>
            <description>Bangla (India)</description>
            <languageList>
              <iso639Id>ben</iso639Id>
              
              <iso639Id>sat</iso639Id>
            </languageList>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>ben_probhat</name>
            
            <shortDescription>bn</shortDescription>
            <description>Bangla (India, Probhat)</description>
            <languageList>
              <iso639Id>ben</iso639Id>
              
              <iso639Id>sat</iso639Id>
            </languageList>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>ben_baishakhi</name>
            <description>Bangla (India, Baishakhi)</description>
            <languageList>
              <iso639Id>ben</iso639Id>
              
              <iso639Id>sat</iso639Id>
            </languageList>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>ben_bornona</name>
            <description>Bangla (India, Bornona)</description>
            <languageList>
              <iso639Id>ben</iso639Id>
              
              <iso639Id>sat</iso639Id>
            </languageList>
          </configItem>
        </variant>
         <variant>
          <configItem>
            <name>ben_gitanjali</name>
            <description>Bangla (India, Uni Gitanjali)</description>
            <languageList>
              <iso639Id>ben</iso639Id>
              
              <iso639Id>sat</iso639Id>
            </languageList>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>ben_inscript</name>
            <description>Bangla (India, Baishakhi Inscript)</description>
            <languageList>
              <iso639Id>ben</iso639Id>
              
              <iso639Id>sat</iso639Id>
            </languageList>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>eeyek</name>
            <description>Manipuri (Eeyek)</description>
            <languageList>
              <iso639Id>mni</iso639Id>
            </languageList>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>guj</name>
            
            <shortDescription>gu</shortDescription>
            <description>Gujarati</description>
            <languageList>
              <iso639Id>guj</iso639Id>
            </languageList>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>guru</name>
            
            <shortDescription>pa</shortDescription>
            <description>Punjabi (Gurmukhi)</description>
            <languageList>
              <iso639Id>pan</iso639Id>
            </languageList>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>jhelum</name>
            
            <shortDescription>pa</shortDescription>
            <description>Punjabi (Gurmukhi Jhelum)</description>
            <languageList>
              <iso639Id>pan</iso639Id>
            </languageList>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>kan</name>
            
            <shortDescription>kn</shortDescription>
            <description>Kannada</description>
            <languageList>
              <iso639Id>kan</iso639Id>
            </languageList>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>kan-kagapa</name>
            
            <shortDescription>kn</shortDescription>
            <description>Kannada (KaGaPa phonetic)</description>
            <languageList>
              <iso639Id>kan</iso639Id>
            </languageList>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>mal</name>
            
            <shortDescription>ml</shortDescription>
            <description>Malayalam</description>
            <languageList>
              <iso639Id>mal</iso639Id>
            </languageList>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>mal_lalitha</name>
            
            <shortDescription>ml</shortDescription>
            <description>Malayalam (Lalitha)</description>
            <languageList>
              <iso639Id>mal</iso639Id>
            </languageList>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>mal_enhanced</name>
            
            <shortDescription>ml</shortDescription>
            <description>Malayalam (enhanced Inscript, with rupee sign)</description>
            <languageList>
              <iso639Id>mal</iso639Id>
            </languageList>
          </configItem>
         </variant>
         <variant>
           <configItem>
            <name>ori</name>
            
            <shortDescription>or</shortDescription>
            <description>Oriya</description>
            <languageList>
              <iso639Id>ori</iso639Id>
              
              <iso639Id>sat</iso639Id>
            </languageList>
          </configItem>
        </variant>
        <variant>
           <configItem>
            <name>olck</name>
            
            <shortDescription>sat</shortDescription>
            <description>Ol Chiki</description>
            <languageList>
              
              <iso639Id>sat</iso639Id>
            </languageList>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>tam_unicode</name>
            
            <shortDescription>ta</shortDescription>
            <description>Tamil (Unicode)</description>
            <languageList>
              <iso639Id>tam</iso639Id>
            </languageList>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>tam_keyboard_with_numerals</name>
            
            <shortDescription>ta</shortDescription>
            <description>Tamil (keyboard with numerals)</description>
            <languageList>
              <iso639Id>tam</iso639Id>
            </languageList>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>tam_TAB</name>
            
            <shortDescription>ta</shortDescription>
            <description>Tamil (TAB typewriter)</description>
            <languageList>
              <iso639Id>tam</iso639Id>
            </languageList>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>tam_TSCII</name>
            
            <shortDescription>ta</shortDescription>
            <description>Tamil (TSCII typewriter)</description>
            <languageList>
              <iso639Id>tam</iso639Id>
            </languageList>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>tam</name>
            
            <shortDescription>ta</shortDescription>
            <description>Tamil</description>
            <languageList>
              <iso639Id>tam</iso639Id>
            </languageList>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>tel</name>
            
            <shortDescription>te</shortDescription>
            <description>Telugu</description>
            <languageList>
              <iso639Id>tel</iso639Id>
            </languageList>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>tel-kagapa</name>
            
            <shortDescription>te</shortDescription>
            <description>Telugu (KaGaPa phonetic)</description>
            <languageList>
              <iso639Id>tel</iso639Id>
            </languageList>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>urd-phonetic</name>
            
            <shortDescription>ur</shortDescription>
            <description>Urdu (phonetic)</description>
            <languageList>
              <iso639Id>urd</iso639Id>
            </languageList>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>urd-phonetic3</name>
            
            <shortDescription>ur</shortDescription>
            <description>Urdu (alternative phonetic)</description>
            <languageList>
              <iso639Id>urd</iso639Id>
            </languageList>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>urd-winkeys</name>
            
            <shortDescription>ur</shortDescription>
            <description>Urdu (WinKeys)</description>
            <languageList>
              <iso639Id>urd</iso639Id>
            </languageList>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>bolnagri</name>
            
            <shortDescription>hi</shortDescription>
            <description>Hindi (Bolnagri)</description>
            <languageList>
              <iso639Id>hin</iso639Id>
            </languageList>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>hin-wx</name>
            
            <shortDescription>hi</shortDescription>
            <description>Hindi (Wx)</description>
            <languageList>
              <iso639Id>hin</iso639Id>
            </languageList>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>hin-kagapa</name>
            
            <shortDescription>hi</shortDescription>
            <description>Hindi (KaGaPa phonetic)</description>
            <languageList>
              <iso639Id>hin</iso639Id>
            </languageList>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>san-kagapa</name>
            
            <shortDescription>sa</shortDescription>
            <description>Sanskrit (KaGaPa phonetic)</description>
            <languageList>
              <iso639Id>san</iso639Id>
            </languageList>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>mar-kagapa</name>
            
            <shortDescription>mr</shortDescription>
            <description>Marathi (KaGaPa phonetic)</description>
            <languageList>
              <iso639Id>mar</iso639Id>
            </languageList>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>eng</name>
            
            <shortDescription>en</shortDescription>
            <description>English (India, with rupee sign)</description>
            <languageList>
              <iso639Id>eng</iso639Id>
            </languageList>
          </configItem>
        </variant>
      </variantList>
    </layout>
    <layout>
      <configItem>
        <name>ba</name>
        
        <shortDescription>bs</shortDescription>
        <description>Bosnian</description>
        <languageList>
          <iso639Id>bos</iso639Id>
        </languageList>
      </configItem>
      <variantList>
        <variant>
          <configItem>
            <name>alternatequotes</name>
            <description>Bosnian (with guillemets for quotes)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>unicode</name>
            <description>Bosnian (with Bosnian digraphs)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>unicodeus</name>
            <description>Bosnian (US keyboard with Bosnian digraphs)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>us</name>
            <description>Bosnian (US keyboard with Bosnian letters)</description>
          </configItem>
        </variant>
      </variantList>
    </layout>
    <layout>
      <configItem>
        <name>br</name>
        
        <shortDescription>pt</shortDescription>
        <description>Portuguese (Brazil)</description>
        <languageList>
          <iso639Id>por</iso639Id>
        </languageList>
      </configItem>
      <variantList>
        <variant>
          <configItem>
            <name>nodeadkeys</name>
            <description>Portuguese (Brazil, eliminate dead keys)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>dvorak</name>
            <description>Portuguese (Brazil, Dvorak)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>nativo</name>
            <description>Portuguese (Brazil, Nativo)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>nativo-us</name>
            <description>Portuguese (Brazil, Nativo for US keyboards)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>nativo-epo</name>
            <description>Esperanto (Brazil, Nativo)</description>
            <languageList>
              <iso639Id>epo</iso639Id>
            </languageList>
          </configItem>
        </variant>
      </variantList>
    </layout>
    <layout>
      <configItem>
        <name>bg</name>
        
        <shortDescription>bg</shortDescription>
        <description>Bulgarian</description>
        <languageList>
          <iso639Id>bul</iso639Id>
        </languageList>
      </configItem>
      <variantList>
        <variant>
          <configItem>
            <name>phonetic</name>
            <description>Bulgarian (traditional phonetic)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>bas_phonetic</name>
            <description>Bulgarian (new phonetic)</description>
          </configItem>
        </variant>
      </variantList>
    </layout>
    <layout>
      <configItem>
        <name>ma</name>
        
        <shortDescription>ar</shortDescription>
        <description>Arabic (Morocco)</description>
      </configItem>
      <variantList>
        <variant>
          <configItem>
            <name>french</name>
            
            <shortDescription>fr</shortDescription>
            <description>French (Morocco)</description>
            <languageList>
              <iso639Id>fra</iso639Id>
            </languageList>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>tifinagh</name>
            
            <shortDescription>ber</shortDescription>
            <description>Berber (Morocco, Tifinagh)</description>
            <languageList>
              <iso639Id>ber</iso639Id>
            </languageList>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>tifinagh-alt</name>
            
            <shortDescription>ber</shortDescription>
            <description>Berber (Morocco, Tifinagh alternative)</description>
            <languageList>
              <iso639Id>ber</iso639Id>
            </languageList>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>tifinagh-alt-phonetic</name>
            
            <shortDescription>ber</shortDescription>
            <description>Berber (Morocco, Tifinagh alternative phonetic)</description>
            <languageList>
              <iso639Id>ber</iso639Id>
            </languageList>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>tifinagh-extended</name>
            
            <shortDescription>ber</shortDescription>
            <description>Berber (Morocco, Tifinagh extended)</description>
            <languageList>
              <iso639Id>ber</iso639Id>
            </languageList>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>tifinagh-phonetic</name>
            
            <shortDescription>ber</shortDescription>
            <description>Berber (Morocco, Tifinagh phonetic)</description>
            <languageList>
              <iso639Id>ber</iso639Id>
            </languageList>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>tifinagh-extended-phonetic</name>
            
            <shortDescription>ber</shortDescription>
            <description>Berber (Morocco, Tifinagh extended phonetic)</description>
            <languageList>
              <iso639Id>ber</iso639Id>
            </languageList>
          </configItem>
        </variant>
      </variantList>
    </layout>
    <layout>
      <configItem>
        <name>cm</name>
        
        <shortDescription>cm</shortDescription>
        <description>English (Cameroon)</description>
        <languageList>
          <iso639Id>eng</iso639Id>
        </languageList>
      </configItem>
      <variantList>
        <variant>
          <configItem>
            <name>french</name>
            <description>French (Cameroon)</description>
            <languageList>
              <iso639Id>fra</iso639Id>
            </languageList>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>qwerty</name>
            <description>Cameroon Multilingual (qwerty)</description>
            <languageList>
              <iso639Id>eng</iso639Id>
              <iso639Id>bas</iso639Id>
              <iso639Id>nmg</iso639Id>
              <iso639Id>fub</iso639Id>
              <iso639Id>ewo</iso639Id>
              <iso639Id>xmd</iso639Id>
              <iso639Id>mfh</iso639Id>
              <iso639Id>bkm</iso639Id>
              <iso639Id>ozm</iso639Id>
              <iso639Id>lns</iso639Id>
              <iso639Id>sox</iso639Id>
              <iso639Id>pny</iso639Id>
              <iso639Id>wes</iso639Id>
              <iso639Id>lem</iso639Id>
              <iso639Id>nyj</iso639Id>
              <iso639Id>mfk</iso639Id>
              <iso639Id>mcp</iso639Id>
              <iso639Id>ass</iso639Id>
              <iso639Id>xed</iso639Id>
              <iso639Id>dua</iso639Id>
              <iso639Id>anv</iso639Id>
              <iso639Id>bum</iso639Id>
              <iso639Id>btb</iso639Id>
              <iso639Id>bfd</iso639Id>
              <iso639Id>azo</iso639Id>
              <iso639Id>ken</iso639Id>
              <iso639Id>yam</iso639Id>
              <iso639Id>yat</iso639Id>
              <iso639Id>yas</iso639Id>
            </languageList>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>azerty</name>
            <description>Cameroon Multilingual (azerty)</description>
            <languageList>
              <iso639Id>fra</iso639Id>
              <iso639Id>bas</iso639Id>
              <iso639Id>nmg</iso639Id>
              <iso639Id>fub</iso639Id>
              <iso639Id>ewo</iso639Id>
              <iso639Id>xmd</iso639Id>
              <iso639Id>mfh</iso639Id>
              <iso639Id>bkm</iso639Id>
              <iso639Id>ozm</iso639Id>
              <iso639Id>lns</iso639Id>
              <iso639Id>sox</iso639Id>
              <iso639Id>pny</iso639Id>
              <iso639Id>wes</iso639Id>
              <iso639Id>lem</iso639Id>
              <iso639Id>nyj</iso639Id>
              <iso639Id>mfk</iso639Id>
              <iso639Id>mcp</iso639Id>
              <iso639Id>ass</iso639Id>
              <iso639Id>xed</iso639Id>
              <iso639Id>dua</iso639Id>
              <iso639Id>anv</iso639Id>
              <iso639Id>bum</iso639Id>
              <iso639Id>btb</iso639Id>
              <iso639Id>bfd</iso639Id>
              <iso639Id>azo</iso639Id>
              <iso639Id>ken</iso639Id>
              <iso639Id>yam</iso639Id>
              <iso639Id>yat</iso639Id>
              <iso639Id>yas</iso639Id>
            </languageList>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>dvorak</name>
            <description>Cameroon Multilingual (Dvorak)</description>
          </configItem>
        </variant>
      </variantList>
    </layout>
    <layout>
      <configItem>
        <name>mm</name>
        
        <shortDescription>my</shortDescription>
        <description>Burmese</description>
        <languageList>
          <iso639Id>mya</iso639Id>
        </languageList>
      </configItem>
      <variantList/>
    </layout>
    <layout>
      <configItem>
        <name>ca</name>
        
        <shortDescription>fr</shortDescription>
        <description>French (Canada)</description>
        <languageList>
          <iso639Id>fra</iso639Id>
        </languageList>
      </configItem>
      <variantList>
        <variant>
          <configItem>
            <name>fr-dvorak</name>
            
            <shortDescription>fr</shortDescription>
            <description>French (Canada, Dvorak)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>fr-legacy</name>
            
            <shortDescription>fr</shortDescription>
            <description>French (Canada, legacy)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>multix</name>
            <description>Canadian Multilingual</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>multi</name>
            <description>Canadian Multilingual (first part)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>multi-2gr</name>
            <description>Canadian Multilingual (second part)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>ike</name>
            
            <shortDescription>ike</shortDescription>
            <description>Inuktitut</description>
            <languageList>
              <iso639Id>iku</iso639Id>
            </languageList>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>eng</name>
            
            <shortDescription>en</shortDescription>
            <description>English (Canada)</description>
            <languageList>
              <iso639Id>eng</iso639Id>
            </languageList>
          </configItem>
        </variant>
      </variantList>
    </layout>
    <layout>
      <configItem>
        <name>cd</name>
        
        <shortDescription>fr</shortDescription>
        <description>French (Democratic Republic of the Congo)</description>
        <languageList>
          <iso639Id>fra</iso639Id>
        </languageList>
      </configItem>
      <variantList/>
    </layout>
    <layout>
      <configItem>
        <name>cn</name>
        
        <shortDescription>zh</shortDescription>
        <description>Chinese</description>
        <languageList>
          <iso639Id>chi</iso639Id>
        </languageList>
      </configItem>
      <variantList>
        <variant>
          <configItem>
            <name>tib</name>
            <description>Tibetan</description>
            <languageList>
              <iso639Id>tib</iso639Id>
            </languageList>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>tib_asciinum</name>
            <description>Tibetan (with ASCII numerals)</description>
            <languageList>
              <iso639Id>tib</iso639Id>
            </languageList>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>ug</name>
            <shortDescription>ug</shortDescription>
            <description>Uyghur</description>
            <languageList>
              <iso639Id>uig</iso639Id>
            </languageList>
          </configItem>
        </variant>
      </variantList>
    </layout>
    <layout>
      <configItem>
        <name>hr</name>
        
        <shortDescription>hr</shortDescription>
        <description>Croatian</description>
        <languageList>
          <iso639Id>hrv</iso639Id>
        </languageList>
      </configItem>
      <variantList>
        <variant>
          <configItem>
            <name>alternatequotes</name>
            <description>Croatian (with guillemets for quotes)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>unicode</name>
            <description>Croatian (with Croatian digraphs)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>unicodeus</name>
            <description>Croatian (US keyboard with Croatian digraphs)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>us</name>
            <description>Croatian (US keyboard with Croatian letters)</description>
          </configItem>
        </variant>
      </variantList>
    </layout>
    <layout>
      <configItem>
        <name>cz</name>
        
        <shortDescription>cs</shortDescription>
        <description>Czech</description>
        <languageList>
          <iso639Id>cze</iso639Id>
        </languageList>
      </configItem>
      <variantList>
        <variant>
          <configItem>
            <name>bksl</name>
            <description>Czech (with &lt;\|&gt; key)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>qwerty</name>
            <description>Czech (qwerty)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>qwerty_bksl</name>
            <description>Czech (qwerty, extended Backslash)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>ucw</name>
            <description>Czech (UCW layout, accented letters only)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>dvorak-ucw</name>
            <description>Czech (US Dvorak with CZ UCW support)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>rus</name>
            
            <shortDescription>ru</shortDescription>
            <description>Russian (Czech, phonetic)</description>
            <languageList>
              <iso639Id>rus</iso639Id>
            </languageList>
          </configItem>
        </variant>
      </variantList>
    </layout>
    <layout>
      <configItem>
        <name>dk</name>
        
        <shortDescription>da</shortDescription>
        <description>Danish</description>
        <languageList>
          <iso639Id>dan</iso639Id>
        </languageList>
      </configItem>
      <variantList>
        <variant>
          <configItem>
            <name>nodeadkeys</name>
            <description>Danish (eliminate dead keys)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>winkeys</name>
            <description>Danish (Winkeys)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>mac</name>
            <description>Danish (Macintosh)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>mac_nodeadkeys</name>
            <description>Danish (Macintosh, eliminate dead keys)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>dvorak</name>
            <description>Danish (Dvorak)</description>
          </configItem>
        </variant>
      </variantList>
    </layout>
    <layout>
      <configItem>
        <name>nl</name>
        
        <shortDescription>nl</shortDescription>
        <description>Dutch</description>
        <languageList>
          <iso639Id>nld</iso639Id>
        </languageList>
      </configItem>
      <variantList>
        <variant>
          <configItem>
            <name>sundeadkeys</name>
            <description>Dutch (Sun dead keys)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>mac</name>
            <description>Dutch (Macintosh)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>std</name>
            <description>Dutch (standard)</description>
          </configItem>
        </variant>
      </variantList>
    </layout>
    <layout>
      <configItem>
        <name>bt</name>
        
        <shortDescription>dz</shortDescription>
        <description>Dzongkha</description>
        <languageList>
          <iso639Id>dzo</iso639Id>
        </languageList>
      </configItem>
    </layout>
    <layout>
      <configItem>
        <name>ee</name>
        
        <shortDescription>et</shortDescription>
        <description>Estonian</description>
        <languageList>
          <iso639Id>est</iso639Id>
        </languageList>
      </configItem>
      <variantList>
        <variant>
          <configItem>
            <name>nodeadkeys</name>
            <description>Estonian (eliminate dead keys)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>dvorak</name>
            <description>Estonian (Dvorak)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>us</name>
            <description>Estonian (US keyboard with Estonian letters)</description>
          </configItem>
        </variant>
      </variantList>
    </layout>
    <layout>
      <configItem>
        <name>ir</name>
        
        <shortDescription>fa</shortDescription>
        <description>Persian</description>
        <languageList>
          <iso639Id>per</iso639Id>
        </languageList>
      </configItem>
      <variantList>
        <variant>
          <configItem>
            <name>pes_keypad</name>
            <description>Persian (with Persian keypad)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>ku</name>
            
            <shortDescription>ku</shortDescription>
            <description>Kurdish (Iran, Latin Q)</description>
            <languageList>
              <iso639Id>kur</iso639Id>
            </languageList>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>ku_f</name>
            
            <shortDescription>ku</shortDescription>
            <description>Kurdish (Iran, F)</description>
            <languageList>
              <iso639Id>kur</iso639Id>
            </languageList>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>ku_alt</name>
            
            <shortDescription>ku</shortDescription>
            <description>Kurdish (Iran, Latin Alt-Q)</description>
            <languageList>
              <iso639Id>kur</iso639Id>
            </languageList>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>ku_ara</name>
            
            <shortDescription>ku</shortDescription>
            <description>Kurdish (Iran, Arabic-Latin)</description>
            <languageList>
              <iso639Id>kur</iso639Id>
            </languageList>
          </configItem>
        </variant>
      </variantList>
    </layout>
    <layout>
      <configItem>
        <name>iq</name>
        
        <shortDescription>ar</shortDescription>
        <description>Iraqi</description>
        <languageList>
          <iso639Id>ara</iso639Id>
          <iso639Id>kur</iso639Id>
        </languageList>
      </configItem>
      <variantList>
        <variant>
          <configItem>
            <name>ku</name>
            
            <shortDescription>ku</shortDescription>
            <description>Kurdish (Iraq, Latin Q)</description>
            <languageList>
              <iso639Id>kur</iso639Id>
            </languageList>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>ku_f</name>
            
            <shortDescription>ku</shortDescription>
            <description>Kurdish (Iraq, F)</description>
            <languageList>
              <iso639Id>kur</iso639Id>
            </languageList>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>ku_alt</name>
            
            <shortDescription>ku</shortDescription>
            <description>Kurdish (Iraq, Latin Alt-Q)</description>
            <languageList>
              <iso639Id>kur</iso639Id>
            </languageList>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>ku_ara</name>
            
            <shortDescription>ku</shortDescription>
            <description>Kurdish (Iraq, Arabic-Latin)</description>
            <languageList>
              <iso639Id>kur</iso639Id>
            </languageList>
          </configItem>
        </variant>
      </variantList>
    </layout>
    <layout>
      <configItem>
        <name>fo</name>
        
        <shortDescription>fo</shortDescription>
        <description>Faroese</description>
        <languageList>
          <iso639Id>fao</iso639Id>
        </languageList>
      </configItem>
      <variantList>
        <variant>
          <configItem>
            <name>nodeadkeys</name>
            <description>Faroese (eliminate dead keys)</description>
          </configItem>
        </variant>
      </variantList>
    </layout>
    <layout>
      <configItem>
        <name>fi</name>
        
        <shortDescription>fi</shortDescription>
        <description>Finnish</description>
        <languageList>
          <iso639Id>fin</iso639Id>
        </languageList>
      </configItem>
      <variantList>
        <variant>
          <configItem>
            <name>classic</name>
            <description>Finnish (classic)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>nodeadkeys</name>
            <description>Finnish (classic, eliminate dead keys)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>winkeys</name>
            <description>Finnish (Winkeys)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>smi</name>
            <description>Northern Saami (Finland)</description>
            <languageList>
              <iso639Id>sme</iso639Id>
            </languageList>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>mac</name>
            <description>Finnish (Macintosh)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>das</name>
            <description>Finnish (DAS)</description>
          </configItem>
        </variant>
      </variantList>
    </layout>
    <layout>
      <configItem>
        <name>fr</name>
        
        <shortDescription>fr</shortDescription>
        <description>French</description>
        <languageList>
          <iso639Id>fra</iso639Id>
        </languageList>
      </configItem>
      <variantList>
        <variant>
          <configItem>
            <name>nodeadkeys</name>
            <description>French (eliminate dead keys)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>sundeadkeys</name>
            <description>French (Sun dead keys)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>oss</name>
            <description>French (alternative)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>oss_latin9</name>
            <description>French (alternative, Latin-9 only)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>oss_nodeadkeys</name>
            <description>French (alternative, eliminate dead keys)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>oss_sundeadkeys</name>
            <description>French (alternative, Sun dead keys)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>latin9</name>
            <description>French (legacy, alternative)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>latin9_nodeadkeys</name>
            <description>French (legacy, alternative, eliminate dead keys)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>latin9_sundeadkeys</name>
            <description>French (legacy, alternative, Sun dead keys)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>bepo</name>
            <description>French (Bepo, ergonomic, Dvorak way)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>bepo_latin9</name>
            <description>French (Bepo, ergonomic, Dvorak way, Latin-9 only)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>dvorak</name>
            <description>French (Dvorak)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>mac</name>
            <description>French (Macintosh)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>bre</name>
            <description>French (Breton)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>oci</name>
            <description>Occitan</description>
            <languageList>
              <iso639Id>oci</iso639Id>
            </languageList>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>geo</name>
            <description>Georgian (France, AZERTY Tskapo)</description>
            <languageList>
              <iso639Id>geo</iso639Id>
            </languageList>
          </configItem>
        </variant>
      </variantList>
    </layout>
    <layout>
      <configItem>
        <name>gh</name>
        
        <shortDescription>en</shortDescription>
        <description>English (Ghana)</description>
        <languageList>
          <iso639Id>eng</iso639Id>
        </languageList>
      </configItem>
      <variantList>
        <variant>
          <configItem>
            <name>generic</name>
            <description>English (Ghana, multilingual)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>akan</name>
            
            <shortDescription>ak</shortDescription>
            <description>Akan</description>
            <languageList>
              <iso639Id>aka</iso639Id>
            </languageList>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>ewe</name>
            
            <shortDescription>ee</shortDescription>
            <description>Ewe</description>
            <languageList>
              <iso639Id>ewe</iso639Id>
            </languageList>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>fula</name>
            
            <shortDescription>ff</shortDescription>
            <description>Fula</description>
            <languageList>
              <iso639Id>ful</iso639Id>
            </languageList>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>ga</name>
            
            <shortDescription>gaa</shortDescription>
            <description>Ga</description>
            <languageList>
              <iso639Id>gaa</iso639Id>
            </languageList>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>hausa</name>
            
            <shortDescription>ha</shortDescription>
            <description>Hausa</description>
            <languageList>
              <iso639Id>hau</iso639Id>
            </languageList>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>avn</name>
            
            <shortDescription>avn</shortDescription>
            <description>Avatime</description>
            <languageList>
              <iso639Id>avn</iso639Id>
            </languageList>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>gillbt</name>
            <description>English (Ghana, GILLBT)</description>
          </configItem>
        </variant>
      </variantList>
    </layout>
    <layout>
      <configItem>
        <name>gn</name>
        
        <shortDescription>fr</shortDescription>
        <description>French (Guinea)</description>
        <languageList>
          <iso639Id>fra</iso639Id>
        </languageList>
      </configItem>
      <variantList/>
    </layout>
    <layout>
      <configItem>
        <name>ge</name>
        
        <shortDescription>ka</shortDescription>
        <description>Georgian</description>
        <languageList>
          <iso639Id>geo</iso639Id>
        </languageList>
      </configItem>
      <variantList>
        <variant>
          <configItem>
            <name>ergonomic</name>
            <description>Georgian (ergonomic)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>mess</name>
            <description>Georgian (MESS)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>ru</name>
            
            <shortDescription>ru</shortDescription>
            <description>Russian (Georgia)</description>
            <languageList>
              <iso639Id>rus</iso639Id>
            </languageList>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>os</name>
            <description>Ossetian (Georgia)</description>
            <languageList>
              <iso639Id>oss</iso639Id>
            </languageList>
          </configItem>
        </variant>
      </variantList>
    </layout>
    <layout>
      <configItem>
        <name>de</name>
        
        <shortDescription>de</shortDescription>
        <description>German</description>
        <languageList>
          <iso639Id>ger</iso639Id>
        </languageList>
      </configItem>
      <variantList>
        <variant>
          <configItem>
            <name>deadacute</name>
            <description>German (dead acute)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>deadgraveacute</name>
            <description>German (dead grave acute)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>nodeadkeys</name>
            <description>German (eliminate dead keys)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>T3</name>
            <description>German (T3)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>ro</name>
            <description>Romanian (Germany)</description>
            <languageList>
              <iso639Id>rum</iso639Id>
            </languageList>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>ro_nodeadkeys</name>
            <description>Romanian (Germany, eliminate dead keys)</description>
            <languageList>
              <iso639Id>rum</iso639Id>
            </languageList>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>dvorak</name>
            <description>German (Dvorak)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>sundeadkeys</name>
            <description>German (Sun dead keys)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>neo</name>
            <description>German (Neo 2)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>mac</name>
            <description>German (Macintosh)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>mac_nodeadkeys</name>
            <description>German (Macintosh, eliminate dead keys)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>dsb</name>
            <description>Lower Sorbian</description>
            <languageList>
              <iso639Id>dsb</iso639Id>
            </languageList>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>dsb_qwertz</name>
            <description>Lower Sorbian (qwertz)</description>
            <languageList>
              <iso639Id>dsb</iso639Id>
            </languageList>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>qwerty</name>
            <description>German (qwerty)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>tr</name>
            <description>Turkish (Germany)</description>
            <languageList>
              <iso639Id>tr</iso639Id>
            </languageList>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>ru</name>
            
            <shortDescription>ru</shortDescription>
            <description>Russian (Germany, phonetic)</description>
            <languageList>
              <iso639Id>rus</iso639Id>
            </languageList>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>legacy</name>
            <description>German (legacy)</description>
          </configItem>
        </variant>
      </variantList>
    </layout>
    <layout>
      <configItem>
        <name>gr</name>
        
        <shortDescription>gr</shortDescription>
        <description>Greek</description>
        <languageList>
          <iso639Id>gre</iso639Id>
        </languageList>
      </configItem>
      <variantList>
        <variant>
          <configItem>
            <name>simple</name>
            <description>Greek (simple)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>extended</name>
            <description>Greek (extended)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>nodeadkeys</name>
            <description>Greek (eliminate dead keys)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>polytonic</name>
            <description>Greek (polytonic)</description>
          </configItem>
        </variant>
      </variantList>
    </layout>
    <layout>
      <configItem>
        <name>hu</name>
        
        <shortDescription>hu</shortDescription>
        <description>Hungarian</description>
        <languageList>
          <iso639Id>hun</iso639Id>
        </languageList>
      </configItem>
      <variantList>
        <variant>
          <configItem>
            <name>standard</name>
            <description>Hungarian (standard)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>nodeadkeys</name>
            <description>Hungarian (eliminate dead keys)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>qwerty</name>
            <description>Hungarian (qwerty)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>101_qwertz_comma_dead</name>
            <description>Hungarian (101/qwertz/comma/dead keys)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>101_qwertz_comma_nodead</name>
            <description>Hungarian (101/qwertz/comma/eliminate dead keys)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>101_qwertz_dot_dead</name>
            <description>Hungarian (101/qwertz/dot/dead keys)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>101_qwertz_dot_nodead</name>
            <description>Hungarian (101/qwertz/dot/eliminate dead keys)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>101_qwerty_comma_dead</name>
            <description>Hungarian (101/qwerty/comma/dead keys)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>101_qwerty_comma_nodead</name>
            <description>Hungarian (101/qwerty/comma/eliminate dead keys)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>101_qwerty_dot_dead</name>
            <description>Hungarian (101/qwerty/dot/dead keys)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>101_qwerty_dot_nodead</name>
            <description>Hungarian (101/qwerty/dot/eliminate dead keys)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>102_qwertz_comma_dead</name>
            <description>Hungarian (102/qwertz/comma/dead keys)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>102_qwertz_comma_nodead</name>
            <description>Hungarian (102/qwertz/comma/eliminate dead keys)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>102_qwertz_dot_dead</name>
            <description>Hungarian (102/qwertz/dot/dead keys)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>102_qwertz_dot_nodead</name>
            <description>Hungarian (102/qwertz/dot/eliminate dead keys)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>102_qwerty_comma_dead</name>
            <description>Hungarian (102/qwerty/comma/dead keys)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>102_qwerty_comma_nodead</name>
            <description>Hungarian (102/qwerty/comma/eliminate dead keys)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>102_qwerty_dot_dead</name>
            <description>Hungarian (102/qwerty/dot/dead keys)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>102_qwerty_dot_nodead</name>
            <description>Hungarian (102/qwerty/dot/eliminate dead keys)</description>
          </configItem>
        </variant>
      </variantList>
    </layout>
    <layout>
      <configItem>
        <name>is</name>
        
        <shortDescription>is</shortDescription>
        <description>Icelandic</description>
        <languageList>
          <iso639Id>ice</iso639Id>
        </languageList>
      </configItem>
      <variantList>
        <variant>
          <configItem>
            <name>Sundeadkeys</name>
            <description>Icelandic (Sun dead keys)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>nodeadkeys</name>
            <description>Icelandic (eliminate dead keys)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>mac_legacy</name>
            <description>Icelandic (Macintosh, legacy)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>mac</name>
            <description>Icelandic (Macintosh)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>dvorak</name>
            <description>Icelandic (Dvorak)</description>
          </configItem>
        </variant>
      </variantList>
    </layout>
    <layout>
      <configItem>
        <name>il</name>
        
        <shortDescription>he</shortDescription>
        <description>Hebrew</description>
        <languageList>
          <iso639Id>heb</iso639Id>
        </languageList>
      </configItem>
      <variantList>
        <variant>
          <configItem>
            <name>lyx</name>
            <description>Hebrew (lyx)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>phonetic</name>
            <description>Hebrew (phonetic)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>biblical</name>
            <description>Hebrew (Biblical, Tiro)</description>
          </configItem>
        </variant>
      </variantList>
    </layout>
    <layout>
      <configItem>
        <name>it</name>
        
        <shortDescription>it</shortDescription>
        <description>Italian</description>
        <languageList>
          <iso639Id>ita</iso639Id>
        </languageList>
      </configItem>
      <variantList>
        <variant>
          <configItem>
            <name>nodeadkeys</name>
            <description>Italian (eliminate dead keys)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>winkeys</name>
            <description>Italian (Winkeys)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>mac</name>
            <description>Italian (Macintosh)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>us</name>
            <description>Italian (US keyboard with Italian letters)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>geo</name>
            <description>Georgian (Italy)</description>
            <languageList>
              <iso639Id>geo</iso639Id>
            </languageList>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>ibm</name>
            <description>Italian (IBM 142)</description>
          </configItem>
        </variant>
      </variantList>
    </layout>
    <layout>
      <configItem>
        <name>jp</name>
        
        <shortDescription>ja</shortDescription>
        <description>Japanese</description>
        <languageList>
          <iso639Id>jpn</iso639Id>
        </languageList>
      </configItem>
      <variantList>
        <variant>
          <configItem>
            <name>kana</name>
            <description>Japanese (Kana)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>kana86</name>
            <description>Japanese (Kana 86)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>OADG109A</name>
            <description>Japanese (OADG 109A)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>mac</name>
            <description>Japanese (Macintosh)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>dvorak</name>
            <description>Japanese (Dvorak)</description>
          </configItem>
        </variant>
      </variantList>
    </layout>
    <layout>
      <configItem>
        <name>kg</name>
        
        <shortDescription>ki</shortDescription>
        <description>Kyrgyz</description>
        <languageList>
          <iso639Id>kir</iso639Id>
        </languageList>
      </configItem>
      <variantList>
        <variant>
          <configItem>
            <name>phonetic</name>
            <description>Kyrgyz (phonetic)</description>
          </configItem>
        </variant>
      </variantList>
    </layout>
    <layout>
      <configItem>
        <name>kh</name>
        
        <shortDescription>km</shortDescription>
        <description>Khmer (Cambodia)</description>
        <languageList>
          <iso639Id>khm</iso639Id>
        </languageList>
      </configItem>
      <variantList/>
    </layout>
    <layout>
      <configItem>
        <name>kz</name>
        
        <shortDescription>kk</shortDescription>
        <description>Kazakh</description>
        <languageList>
          <iso639Id>kaz</iso639Id>
        </languageList>
      </configItem>
      <variantList>
        <variant>
          <configItem>
            <name>ruskaz</name>
            
            <shortDescription>ru</shortDescription>
            <description>Russian (Kazakhstan, with Kazakh)</description>
            <languageList>
              <iso639Id>kaz</iso639Id>
              <iso639Id>rus</iso639Id>
            </languageList>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>kazrus</name>
            <description>Kazakh (with Russian)</description>
            <languageList>
              <iso639Id>kaz</iso639Id>
              <iso639Id>rus</iso639Id>
            </languageList>
          </configItem>
        </variant>
      </variantList>
    </layout>
    <layout>
      <configItem>
        <name>la</name>
        
        <shortDescription>lo</shortDescription>
        <description>Lao</description>
        <languageList>
          <iso639Id>lao</iso639Id>
        </languageList>
      </configItem>
      <variantList>
        <variant>
          <configItem>
            <name>stea</name>
            <description>Lao (STEA proposed standard layout)</description>
            <languageList>
              <iso639Id>lao</iso639Id>
            </languageList>
          </configItem>
        </variant>
      </variantList>
    </layout>
    <layout>
      <configItem>
        <name>latam</name>
        
        <shortDescription>es</shortDescription>
        <description>Spanish (Latin American)</description>
        <countryList>
          <iso3166Id>AR</iso3166Id>
          <iso3166Id>BO</iso3166Id>
          <iso3166Id>CL</iso3166Id>
          <iso3166Id>CO</iso3166Id>
          <iso3166Id>CR</iso3166Id>
          <iso3166Id>CU</iso3166Id>
          <iso3166Id>DO</iso3166Id>
          <iso3166Id>EC</iso3166Id>
          <iso3166Id>GT</iso3166Id>
          <iso3166Id>HN</iso3166Id>
          <iso3166Id>HT</iso3166Id>
          <iso3166Id>MX</iso3166Id>
          <iso3166Id>NI</iso3166Id>
          <iso3166Id>PA</iso3166Id>
          <iso3166Id>PE</iso3166Id>
          <iso3166Id>PR</iso3166Id>
          <iso3166Id>PY</iso3166Id>
          <iso3166Id>SV</iso3166Id>
          <iso3166Id>US</iso3166Id>
          <iso3166Id>UY</iso3166Id>
          <iso3166Id>VE</iso3166Id>
        </countryList>
        <languageList>
          <iso639Id>spa</iso639Id>
        </languageList>
      </configItem>
      <variantList>
        <variant>
          <configItem>
            <name>nodeadkeys</name>
            <description>Spanish (Latin American, eliminate dead keys)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>deadtilde</name>
            <description>Spanish (Latin American, include dead tilde)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>sundeadkeys</name>
            <description>Spanish (Latin American, Sun dead keys)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>dvorak</name>
            <description>Spanish (Latin American, Dvorak)</description>
          </configItem>
        </variant>
      </variantList>
    </layout>
    <layout>
      <configItem>
        <name>lt</name>
        
        <shortDescription>lt</shortDescription>
        <description>Lithuanian</description>
        <languageList>
          <iso639Id>lit</iso639Id>
        </languageList>
      </configItem>
      <variantList>
        <variant>
          <configItem>
            <name>std</name>
            <description>Lithuanian (standard)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>us</name>
            <description>Lithuanian (US keyboard with Lithuanian letters)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>ibm</name>
            <description>Lithuanian (IBM LST 1205-92)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>lekp</name>
            <description>Lithuanian (LEKP)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>lekpa</name>
            <description>Lithuanian (LEKPa)</description>
          </configItem>
        </variant>
      </variantList>
    </layout>
    <layout>
      <configItem>
        <name>lv</name>
        
        <shortDescription>lv</shortDescription>
        <description>Latvian</description>
        <languageList>
          <iso639Id>lav</iso639Id>
        </languageList>
      </configItem>
      <variantList>
        <variant>
          <configItem>
            <name>apostrophe</name>
            <description>Latvian (apostrophe variant)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>tilde</name>
            <description>Latvian (tilde variant)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>fkey</name>
            <description>Latvian (F variant)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>modern</name>
            <description>Latvian (modern)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>ergonomic</name>
            <description>Latvian (ergonomic, ŪGJRMV)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>adapted</name>
            <description>Latvian (adapted)</description>
          </configItem>
        </variant>
      </variantList>
    </layout>
    <layout>
      <configItem>
        <name>mao</name>
        
        <shortDescription>mi</shortDescription>
        <description>Maori</description>
        <languageList>
          <iso639Id>mao</iso639Id>
        </languageList>
      </configItem>
      <variantList/>
    </layout>
    <layout>
      <configItem>
        <name>me</name>
        
        <shortDescription>sr</shortDescription>
        <description>Montenegrin</description>
        <languageList>
          <iso639Id>srp</iso639Id>
        </languageList>
      </configItem>
      <variantList>
        <variant>
          <configItem>
            <name>cyrillic</name>
            <description>Montenegrin (Cyrillic)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>cyrillicyz</name>
            <description>Montenegrin (Cyrillic, ZE and ZHE swapped)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>latinunicode</name>
            <description>Montenegrin (Latin Unicode)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>latinyz</name>
            <description>Montenegrin (Latin qwerty)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>latinunicodeyz</name>
            <description>Montenegrin (Latin Unicode qwerty)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>cyrillicalternatequotes</name>
            <description>Montenegrin (Cyrillic with guillemets)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>latinalternatequotes</name>
            <description>Montenegrin (Latin with guillemets)</description>
          </configItem>
        </variant>
      </variantList>
    </layout>
    <layout>
      <configItem>
        <name>mk</name>
        
        <shortDescription>mk</shortDescription>
        <description>Macedonian</description>
        <languageList>
          <iso639Id>mkd</iso639Id>
        </languageList>
      </configItem>
      <variantList>
        <variant>
          <configItem>
            <name>nodeadkeys</name>
            <description>Macedonian (eliminate dead keys)</description>
          </configItem>
        </variant>
      </variantList>
    </layout>
    <layout>
      <configItem>
        <name>mt</name>
        
        <shortDescription>mt</shortDescription>
        <description>Maltese</description>
        <languageList>
          <iso639Id>mlt</iso639Id>
        </languageList>
      </configItem>
      <variantList>
        <variant>
          <configItem>
            <name>us</name>
            <description>Maltese (with US layout)</description>
          </configItem>
        </variant>
      </variantList>
    </layout>
    <layout>
      <configItem>
        <name>mn</name>
        
        <shortDescription>mn</shortDescription>
        <description>Mongolian</description>
        <languageList>
          <iso639Id>mon</iso639Id>
        </languageList>
      </configItem>
      <variantList/>
    </layout>
    <layout>
      <configItem>
        <name>no</name>
        
        <shortDescription>no</shortDescription>
        <description>Norwegian</description>
        <languageList>
          <iso639Id>nor</iso639Id>
          <iso639Id>nob</iso639Id>
          <iso639Id>nno</iso639Id>
        </languageList>
      </configItem>
      <variantList>
        <variant>
          <configItem>
            <name>nodeadkeys</name>
            <description>Norwegian (eliminate dead keys)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>winkeys</name>
            <description>Norwegian (Winkeys)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>dvorak</name>
            <description>Norwegian (Dvorak)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>smi</name>
            <description>Northern Saami (Norway)</description>
            <languageList>
              <iso639Id>sme</iso639Id>
            </languageList>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>smi_nodeadkeys</name>
            <description>Northern Saami (Norway, eliminate dead keys)</description>
            <languageList>
              <iso639Id>sme</iso639Id>
            </languageList>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>mac</name>
            <description>Norwegian (Macintosh)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>mac_nodeadkeys</name>
            <description>Norwegian (Macintosh, eliminate dead keys)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>colemak</name>
            <description>Norwegian (Colemak)</description>
          </configItem>
        </variant>
      </variantList>
    </layout>
    <layout>
      <configItem>
        <name>pl</name>
        
        <shortDescription>pl</shortDescription>
        <description>Polish</description>
        <languageList>
          <iso639Id>pol</iso639Id>
        </languageList>
      </configItem>
      <variantList>
        <variant>
          <configItem>
            <name>legacy</name>
            <description>Polish (legacy)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>qwertz</name>
            <description>Polish (qwertz)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>dvorak</name>
            <description>Polish (Dvorak)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>dvorak_quotes</name>
            <description>Polish (Dvorak, Polish quotes on quotemark key)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>dvorak_altquotes</name>
            <description>Polish (Dvorak, Polish quotes on key 1)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>csb</name>
            <description>Kashubian</description>
            <languageList>
              <iso639Id>csb</iso639Id>
            </languageList>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>szl</name>
            <description>Silesian</description>
            <languageList>
              <iso639Id>szl</iso639Id>
            </languageList>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>ru_phonetic_dvorak</name>
            
            <shortDescription>ru</shortDescription>
            <description>Russian (Poland, phonetic Dvorak)</description>
            <languageList>
              <iso639Id>rus</iso639Id>
            </languageList>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>dvp</name>
            <description>Polish (programmer Dvorak)</description>
          </configItem>
        </variant>
      </variantList>
    </layout>
    <layout>
      <configItem>
        <name>pt</name>
        
        <shortDescription>pt</shortDescription>
        <description>Portuguese</description>
        <languageList>
          <iso639Id>por</iso639Id>
        </languageList>
      </configItem>
      <variantList>
        <variant>
          <configItem>
            <name>nodeadkeys</name>
            <description>Portuguese (eliminate dead keys)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>sundeadkeys</name>
            <description>Portuguese (Sun dead keys)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>mac</name>
            <description>Portuguese (Macintosh)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>mac_nodeadkeys</name>
            <description>Portuguese (Macintosh, eliminate dead keys)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>mac_sundeadkeys</name>
            <description>Portuguese (Macintosh, Sun dead keys)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>nativo</name>
            <description>Portuguese (Nativo)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>nativo-us</name>
            <description>Portuguese (Nativo for US keyboards)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>nativo-epo</name>
            <description>Esperanto (Portugal, Nativo)</description>
            <languageList>
              <iso639Id>epo</iso639Id>
            </languageList>
          </configItem>
        </variant>
      </variantList>
    </layout>
    <layout>
      <configItem>
        <name>ro</name>
        
        <shortDescription>ro</shortDescription>
        <description>Romanian</description>
        <languageList>
          <iso639Id>rum</iso639Id>
        </languageList>
      </configItem>
      <variantList>
        <variant>
          <configItem>
            <name>cedilla</name>
            <description>Romanian (cedilla)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>std</name>
            <description>Romanian (standard)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>std_cedilla</name>
            <description>Romanian (standard cedilla)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>winkeys</name>
            <description>Romanian (WinKeys)</description>
          </configItem>
        </variant>
      </variantList>
    </layout>
    <layout>
      <configItem>
        <name>ru</name>
        
        <shortDescription>ru</shortDescription>
        <description>Russian</description>
        <languageList>
          <iso639Id>rus</iso639Id>
        </languageList>
      </configItem>
      <variantList>
        <variant>
          <configItem>
            <name>phonetic</name>
            <description>Russian (phonetic)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>phonetic_winkeys</name>
            <description>Russian (phonetic WinKeys)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>typewriter</name>
            <description>Russian (typewriter)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>legacy</name>
            <description>Russian (legacy)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>typewriter-legacy</name>
            <description>Russian (typewriter, legacy)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>tt</name>
            <description>Tatar</description>
            <languageList>
              <iso639Id>tat</iso639Id>
            </languageList>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>os_legacy</name>
            <description>Ossetian (legacy)</description>
            <languageList>
              <iso639Id>oss</iso639Id>
            </languageList>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>os_winkeys</name>
            <description>Ossetian (WinKeys)</description>
            <languageList>
              <iso639Id>oss</iso639Id>
            </languageList>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>cv</name>
            <description>Chuvash</description>
            <languageList>
              <iso639Id>chv</iso639Id>
            </languageList>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>cv_latin</name>
            <description>Chuvash (Latin)</description>
            <languageList>
              <iso639Id>chv</iso639Id>
            </languageList>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>udm</name>
            <description>Udmurt</description>
            <languageList>
              <iso639Id>udm</iso639Id>
            </languageList>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>kom</name>
            <description>Komi</description>
            <languageList>
              <iso639Id>kom</iso639Id>
            </languageList>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>sah</name>
            <description>Yakut</description>
            <languageList>
              <iso639Id>sah</iso639Id>
            </languageList>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>xal</name>
            <description>Kalmyk</description>
            <languageList>
              <iso639Id>xal</iso639Id>
            </languageList>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>dos</name>
            <description>Russian (DOS)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>mac</name>
            <description>Russian (Macintosh)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>srp</name>
            <description>Serbian (Russia)</description>
            <languageList>
              <iso639Id>rus</iso639Id>
              <iso639Id>srp</iso639Id>
            </languageList>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>bak</name>
            <description>Bashkirian</description>
            <languageList>
              <iso639Id>bak</iso639Id>
            </languageList>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>chm</name>
            <description>Mari</description>
            <languageList>
              <iso639Id>chm</iso639Id>
            </languageList>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>phonetic_azerty</name>
            <description>Russian (phonetic azerty)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>phonetic_fr</name>
            <description>Russian (phonetic French)</description>
          </configItem>
        </variant>
      </variantList>
    </layout>
    <layout>
      <configItem>
        <name>rs</name>
        
        <shortDescription>sr</shortDescription>
        <description>Serbian</description>
        <languageList>
          <iso639Id>srp</iso639Id>
        </languageList>
      </configItem>
      <variantList>
        <variant>
          <configItem>
            <name>yz</name>
            <description>Serbian (Cyrillic, ZE and ZHE swapped)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>latin</name>
            <description>Serbian (Latin)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>latinunicode</name>
            <description>Serbian (Latin Unicode)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>latinyz</name>
            <description>Serbian (Latin qwerty)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>latinunicodeyz</name>
            <description>Serbian (Latin Unicode qwerty)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>alternatequotes</name>
            <description>Serbian (Cyrillic with guillemets)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>latinalternatequotes</name>
            <description>Serbian (Latin with guillemets)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>rue</name>
            <description>Pannonian Rusyn</description>
            <languageList>
              <iso639Id>rue</iso639Id>
            </languageList>
          </configItem>
        </variant>
      </variantList>
    </layout>
    <layout>
      <configItem>
        <name>si</name>
        
        <shortDescription>sl</shortDescription>
        <description>Slovenian</description>
        <languageList>
          <iso639Id>slv</iso639Id>
        </languageList>
      </configItem>
      <variantList>
        <variant>
          <configItem>
            <name>alternatequotes</name>
            <description>Slovenian (with guillemets for quotes)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>us</name>
            <description>Slovenian (US keyboard with Slovenian letters)</description>
          </configItem>
        </variant>
      </variantList>
    </layout>
    <layout>
      <configItem>
        <name>sk</name>
        
        <shortDescription>sk</shortDescription>
        <description>Slovak</description>
        <languageList>
          <iso639Id>slo</iso639Id>
        </languageList>
      </configItem>
      <variantList>
        <variant>
          <configItem>
            <name>bksl</name>
            <description>Slovak (extended Backslash)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>qwerty</name>
            <description>Slovak (qwerty)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>qwerty_bksl</name>
            <description>Slovak (qwerty, extended Backslash)</description>
          </configItem>
        </variant>
      </variantList>
    </layout>
    <layout>
      <configItem>
        <name>es</name>
        
        <shortDescription>es</shortDescription>
        <description>Spanish</description>
        <languageList>
          <iso639Id>spa</iso639Id>
        </languageList>
      </configItem>
      <variantList>
        <variant>
          <configItem>
            <name>nodeadkeys</name>
            <description>Spanish (eliminate dead keys)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>winkeys</name>
            <description>Spanish (Winkeys)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>deadtilde</name>
            <description>Spanish (include dead tilde)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>sundeadkeys</name>
            <description>Spanish (Sun dead keys)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>dvorak</name>
            <description>Spanish (Dvorak)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>ast</name>
            <description>Asturian (Spain, with bottom-dot H and bottom-dot L)</description>
            <languageList>
              <iso639Id>ast</iso639Id>
            </languageList>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>cat</name>
            <description>Catalan (Spain, with middle-dot L)</description>
            <languageList>
              <iso639Id>cat</iso639Id>
            </languageList>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>mac</name>
            <description>Spanish (Macintosh)</description>
          </configItem>
        </variant>
      </variantList>
    </layout>
    <layout>
      <configItem>
        <name>se</name>
        
        <shortDescription>sv</shortDescription>
        <description>Swedish</description>
        <languageList>
          <iso639Id>swe</iso639Id>
        </languageList>
      </configItem>
      <variantList>
        <variant>
          <configItem>
            <name>nodeadkeys</name>
            <description>Swedish (eliminate dead keys)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>dvorak</name>
            <description>Swedish (Dvorak)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>rus</name>
            
            <shortDescription>ru</shortDescription>
            <description>Russian (Sweden, phonetic)</description>
            <languageList>
              <iso639Id>rus</iso639Id>
            </languageList>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>rus_nodeadkeys</name>
            
            <shortDescription>ru</shortDescription>
            <description>Russian (Sweden, phonetic, eliminate dead keys)</description>
            <languageList>
              <iso639Id>rus</iso639Id>
            </languageList>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>smi</name>
            <description>Northern Saami (Sweden)</description>
            <languageList>
              <iso639Id>sme</iso639Id>
            </languageList>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>mac</name>
            <description>Swedish (Macintosh)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>svdvorak</name>
            <description>Swedish (Svdvorak)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>swl</name>
            <description>Swedish Sign Language</description>
            <languageList>
              <iso639Id>swl</iso639Id>
            </languageList>
          </configItem>
        </variant>
      </variantList>
    </layout>
    <layout>
      <configItem>
        <name>ch</name>
        
        <shortDescription>de</shortDescription>
        <description>German (Switzerland)</description>
        <languageList>
          <iso639Id>ger</iso639Id>
          <iso639Id>gsw</iso639Id>
        </languageList>
      </configItem>
      <variantList>
        <variant>
          <configItem>
            <name>legacy</name>
            <description>German (Switzerland, legacy)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>de_nodeadkeys</name>
            
            <shortDescription>de</shortDescription>
            <description>German (Switzerland, eliminate dead keys)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>de_sundeadkeys</name>
            
            <shortDescription>de</shortDescription>
            <description>German (Switzerland, Sun dead keys)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>fr</name>
            
            <shortDescription>fr</shortDescription>
            <description>French (Switzerland)</description>
            <languageList>
              <iso639Id>fra</iso639Id>
            </languageList>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>fr_nodeadkeys</name>
            
            <shortDescription>fr</shortDescription>
            <description>French (Switzerland, eliminate dead keys)</description>
            <languageList>
              <iso639Id>fra</iso639Id>
            </languageList>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>fr_sundeadkeys</name>
            
            <shortDescription>fr</shortDescription>
            <description>French (Switzerland, Sun dead keys)</description>
            <languageList>
              <iso639Id>fra</iso639Id>
            </languageList>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>fr_mac</name>
            
            <shortDescription>fr</shortDescription>
            <description>French (Switzerland, Macintosh)</description>
            <languageList>
              <iso639Id>fra</iso639Id>
            </languageList>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>de_mac</name>
            
            <shortDescription>de</shortDescription>
            <description>German (Switzerland, Macintosh)</description>
          </configItem>
        </variant>
      </variantList>
    </layout>
    <layout>
      <configItem>
        <name>sy</name>
        
        <shortDescription>ar</shortDescription>
        <description>Arabic (Syria)</description>
        <languageList>
          <iso639Id>syr</iso639Id>
        </languageList>
      </configItem>
      <variantList>
        <variant>
          <configItem>
            <name>syc</name>
            
            <shortDescription>syc</shortDescription>
            <description>Syriac</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>syc_phonetic</name>
            
            <shortDescription>syc</shortDescription>
            <description>Syriac (phonetic)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>ku</name>
            
            <shortDescription>ku</shortDescription>
            <description>Kurdish (Syria, Latin Q)</description>
            <languageList>
              <iso639Id>kur</iso639Id>
            </languageList>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>ku_f</name>
            
            <shortDescription>ku</shortDescription>
            <description>Kurdish (Syria, F)</description>
            <languageList>
              <iso639Id>kur</iso639Id>
            </languageList>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>ku_alt</name>
            
            <shortDescription>ku</shortDescription>
            <description>Kurdish (Syria, Latin Alt-Q)</description>
            <languageList>
              <iso639Id>kur</iso639Id>
            </languageList>
          </configItem>
        </variant>
      </variantList>
    </layout>
    <layout>
      <configItem>
        <name>tj</name>
        
        <shortDescription>tg</shortDescription>
        <description>Tajik</description>
        <languageList>
          <iso639Id>tgk</iso639Id>
        </languageList>
      </configItem>
      <variantList>
        <variant>
          <configItem>
            <name>legacy</name>
            <description>Tajik (legacy)</description>
          </configItem>
        </variant>
      </variantList>
    </layout>
    <layout>
      <configItem>
        <name>lk</name>
        
        <shortDescription>si</shortDescription>
        <description>Sinhala (phonetic)</description>
        <languageList>
          <iso639Id>sin</iso639Id>
        </languageList>
      </configItem>
      <variantList>
        <variant>
          <configItem>
            <name>tam_unicode</name>
            
            <shortDescription>ta</shortDescription>
            <description>Tamil (Sri Lanka, Unicode)</description>
            <languageList>
              <iso639Id>tam</iso639Id>
            </languageList>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>tam_TAB</name>
            <description>Tamil (Sri Lanka, TAB Typewriter)</description>
            <languageList>
              <iso639Id>tam</iso639Id>
            </languageList>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>us</name>
            <shortDescription>us</shortDescription>
            <description>Sinhala (US keyboard with Sinhala letters)</description>
          </configItem>
        </variant>
      </variantList>
    </layout>
    <layout>
      <configItem>
        <name>th</name>
        
        <shortDescription>th</shortDescription>
        <description>Thai</description>
        <languageList>
          <iso639Id>tha</iso639Id>
        </languageList>
      </configItem>
      <variantList>
        <variant>
          <configItem>
            <name>tis</name>
            <description>Thai (TIS-820.2538)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>pat</name>
            <description>Thai (Pattachote)</description>
          </configItem>
        </variant>
      </variantList>
    </layout>
    <layout>
      <configItem>
        <name>tr</name>
        
        <shortDescription>tr</shortDescription>
        <description>Turkish</description>
        <languageList>
          <iso639Id>tur</iso639Id>
        </languageList>
      </configItem>
      <variantList>
        <variant>
          <configItem>
            <name>f</name>
            <description>Turkish (F)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>alt</name>
            <description>Turkish (Alt-Q)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>sundeadkeys</name>
            <description>Turkish (Sun dead keys)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>ku</name>
            
            <shortDescription>ku</shortDescription>
            <description>Kurdish (Turkey, Latin Q)</description>
            <languageList>
              <iso639Id>kur</iso639Id>
            </languageList>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>ku_f</name>
            
            <shortDescription>ku</shortDescription>
            <description>Kurdish (Turkey, F)</description>
            <languageList>
              <iso639Id>kur</iso639Id>
            </languageList>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>ku_alt</name>
            
            <shortDescription>ku</shortDescription>
            <description>Kurdish (Turkey, Latin Alt-Q)</description>
            <languageList>
              <iso639Id>kur</iso639Id>
            </languageList>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>intl</name>
            <description>Turkish (international with dead keys)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>crh</name>
            
            <shortDescription>crh</shortDescription>
            <description>Crimean Tatar (Turkish Q)</description>
            <languageList>
              <iso639Id>crh</iso639Id>
            </languageList>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>crh_f</name>
            
            <shortDescription>crh</shortDescription>
            <description>Crimean Tatar (Turkish F)</description>
            <languageList>
              <iso639Id>crh</iso639Id>
            </languageList>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>crh_alt</name>
            
            <shortDescription>crh</shortDescription>
            <description>Crimean Tatar (Turkish Alt-Q)</description>
            <languageList>
              <iso639Id>crh</iso639Id>
            </languageList>
          </configItem>
        </variant>
      </variantList>
    </layout>
    <layout>
      <configItem>
        <name>tw</name>
        
        <shortDescription>zh</shortDescription>
        <description>Taiwanese</description>
        <languageList>
          <iso639Id>fox</iso639Id>
        </languageList>
      </configItem>
      <variantList>
        <variant>
          <configItem>
            <name>indigenous</name>
            <description>Taiwanese (indigenous)</description>
            <languageList>
              <iso639Id>ami</iso639Id>
              <iso639Id>tay</iso639Id>
              <iso639Id>bnn</iso639Id>
              <iso639Id>ckv</iso639Id>
              <iso639Id>pwn</iso639Id>
              <iso639Id>pyu</iso639Id>
              <iso639Id>dru</iso639Id>
              <iso639Id>ais</iso639Id>
              <iso639Id>ssf</iso639Id>
              <iso639Id>tao</iso639Id>
              <iso639Id>tsu</iso639Id>
              <iso639Id>trv</iso639Id>
              <iso639Id>xnb</iso639Id>
              <iso639Id>sxr</iso639Id>
              <iso639Id>uun</iso639Id>
              <iso639Id>fos</iso639Id>
            </languageList>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>saisiyat</name>
            
            <shortDescription>xsy</shortDescription>
            <description>Saisiyat (Taiwan)</description>
            <languageList>
              <iso639Id>xsy</iso639Id>
            </languageList>
          </configItem>
        </variant>
      </variantList>
    </layout>
    <layout>
      <configItem>
        <name>ua</name>
        
        <shortDescription>uk</shortDescription>
        <description>Ukrainian</description>
        <languageList>
          <iso639Id>ukr</iso639Id>
        </languageList>
      </configItem>
      <variantList>
        <variant>
          <configItem>
            <name>phonetic</name>
            <description>Ukrainian (phonetic)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>typewriter</name>
            <description>Ukrainian (typewriter)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>winkeys</name>
            <description>Ukrainian (WinKeys)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>legacy</name>
            <description>Ukrainian (legacy)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>rstu</name>
            <description>Ukrainian (standard RSTU)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>rstu_ru</name>
            <description>Russian (Ukraine, standard RSTU)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>homophonic</name>
            <description>Ukrainian (homophonic)</description>
          </configItem>
        </variant>
      </variantList>
    </layout>
    <layout>
      <configItem>
        <name>gb</name>
        
        <shortDescription>en</shortDescription>
        <description>English (UK)</description>
        <languageList>
          <iso639Id>eng</iso639Id>
        </languageList>
      </configItem>
      <variantList>
        <variant>
          <configItem>
            <name>extd</name>
            <description>English (UK, extended WinKeys)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>intl</name>
            <description>English (UK, international with dead keys)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>dvorak</name>
            <description>English (UK, Dvorak)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>dvorakukp</name>
            <description>English (UK, Dvorak with UK punctuation)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>mac</name>
            <description>English (UK, Macintosh)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>mac_intl</name>
            <description>English (UK, Macintosh international)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>colemak</name>
            <description>English (UK, Colemak)</description>
          </configItem>
        </variant>
      </variantList>
    </layout>
    <layout>
      <configItem>
        <name>uz</name>
        
        <shortDescription>uz</shortDescription>
        <description>Uzbek</description>
        <languageList>
          <iso639Id>uzb</iso639Id>
        </languageList>
      </configItem>
      <variantList>
        <variant>
          <configItem>
            <name>latin</name>
            <description>Uzbek (Latin)</description>
          </configItem>
        </variant>
      </variantList>
    </layout>
    <layout>
      <configItem>
        <name>vn</name>
        
        <shortDescription>vi</shortDescription>
        <description>Vietnamese</description>
        <languageList>
          <iso639Id>vie</iso639Id>
        </languageList>
      </configItem>
      <variantList/>
    </layout>
    <layout>
      <configItem>
        <name>kr</name>
        
        <shortDescription>ko</shortDescription>
        <description>Korean</description>
        <languageList>
          <iso639Id>kor</iso639Id>
        </languageList>
      </configItem>
      <variantList>
        <variant>
          <configItem>
            <name>kr104</name>
            <description>Korean (101/104 key compatible)</description>
          </configItem>
        </variant>
      </variantList>
    </layout>
    <layout>
      <configItem>
        <name>nec_vndr/jp</name>
        
        <shortDescription>ja</shortDescription>
        <description>Japanese (PC-98xx Series)</description>
        <countryList>
          <iso3166Id>JP</iso3166Id>
        </countryList>
        <languageList>
          <iso639Id>jpn</iso639Id>
        </languageList>
      </configItem>
      <variantList/>
    </layout>
    <layout>
      <configItem>
        <name>ie</name>
        
        <shortDescription>ie</shortDescription>
        <description>Irish</description>
        <languageList>
          <iso639Id>eng</iso639Id>
        </languageList>
      </configItem>
      <variantList>
        <variant>
          <configItem>
            <name>CloGaelach</name>
            <description>CloGaelach</description>
            <languageList>
              <iso639Id>gle</iso639Id>
            </languageList>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>UnicodeExpert</name>
            <description>Irish (UnicodeExpert)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>ogam</name>
            <description>Ogham</description>
            <languageList>
              <iso639Id>sga</iso639Id>
            </languageList>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>ogam_is434</name>
            <description>Ogham (IS434)</description>
            <languageList>
              <iso639Id>sga</iso639Id>
            </languageList>
          </configItem>
        </variant>
      </variantList>
    </layout>
    <layout>
      <configItem>
        <name>pk</name>
        
        <shortDescription>ur</shortDescription>
        <description>Urdu (Pakistan)</description>
        <languageList>
          <iso639Id>urd</iso639Id>
        </languageList>
      </configItem>
      <variantList>
        <variant>
          <configItem>
            <name>urd-crulp</name>
            <description>Urdu (Pakistan, CRULP)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>urd-nla</name>
            <description>Urdu (Pakistan, NLA)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>ara</name>
            <shortDescription>ar</shortDescription>
            <description>Arabic (Pakistan)</description>
            <languageList>
              <iso639Id>ara</iso639Id>
            </languageList>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>snd</name>
            
            <shortDescription>sd</shortDescription>
            <description>Sindhi</description>
            <languageList>
              <iso639Id>snd</iso639Id>
            </languageList>
          </configItem>
        </variant>
      </variantList>
    </layout>
    <layout>
      <configItem>
        <name>mv</name>
        
        <shortDescription>dv</shortDescription>
        <description>Dhivehi</description>
        <languageList>
          <iso639Id>div</iso639Id>
        </languageList>
      </configItem>
      <variantList/>
    </layout>
    <layout>
      <configItem>
        <name>za</name>
        
        <shortDescription>en</shortDescription>
        <description>English (South Africa)</description>
        <languageList>
          <iso639Id>eng</iso639Id>
        </languageList>
      </configItem>
    </layout>
    <layout>
      <configItem>
        <name>epo</name>
        
        <shortDescription>eo</shortDescription>
        <description>Esperanto</description>
        <languageList>
          <iso639Id>epo</iso639Id>
        </languageList>
      </configItem>
      <variantList>
        <variant>
          <configItem>
            <name>legacy</name>
            <description>Esperanto (displaced semicolon and quote, obsolete)</description>
          </configItem>
        </variant>
      </variantList>
    </layout>
    <layout>
      <configItem>
        <name>np</name>
        
        <shortDescription>ne</shortDescription>
        <description>Nepali</description>
        <languageList>
          <iso639Id>nep</iso639Id>
          
          <iso639Id>sat</iso639Id>
        </languageList>
      </configItem>
    </layout>
    <layout>
      <configItem>
        <name>ng</name>
        
        <shortDescription>en</shortDescription>
        <description>English (Nigeria)</description>
        <languageList>
          <iso639Id>eng</iso639Id>
        </languageList>
      </configItem>
      <variantList>
        <variant>
          <configItem>
            <name>igbo</name>
            
            <shortDescription>ig</shortDescription>
            <description>Igbo</description>
            <languageList>
              <iso639Id>ibo</iso639Id>
            </languageList>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>yoruba</name>
            
            <shortDescription>yo</shortDescription>
            <description>Yoruba</description>
            <languageList>
              <iso639Id>yor</iso639Id>
            </languageList>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>hausa</name>
            
            <shortDescription>ha</shortDescription>
            <description>Hausa</description>
            <languageList>
              <iso639Id>hau</iso639Id>
            </languageList>
          </configItem>
        </variant>
      </variantList>
    </layout>
    <layout>
      <configItem>
        <name>et</name>
        
        <shortDescription>am</shortDescription>
        <description>Amharic</description>
        <languageList>
          <iso639Id>amh</iso639Id>
        </languageList>
      </configItem>
      <variantList/>
    </layout>
    <layout>
      <configItem>
        <name>sn</name>
        
        <shortDescription>wo</shortDescription>
        <description>Wolof</description>
        <languageList>
          <iso639Id>wol</iso639Id>
        </languageList>
      </configItem>
      <variantList/>
    </layout>
    <layout>
      <configItem>
        <name>brai</name>
        
        <shortDescription>brl</shortDescription>
        <description>Braille</description>
      </configItem>
      <variantList>
        <variant>
          <configItem>
            <name>left_hand</name>
            <description>Braille (left hand)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>right_hand</name>
            <description>Braille (right hand)</description>
          </configItem>
        </variant>
      </variantList>
    </layout>
    <layout>
      <configItem>
        <name>tm</name>
        
        <shortDescription>tk</shortDescription>
        <description>Turkmen</description>
        <languageList>
          <iso639Id>tuk</iso639Id>
        </languageList>
      </configItem>
      <variantList>
        <variant>
          <configItem>
            <name>alt</name>
            <description>Turkmen (Alt-Q)</description>
          </configItem>
        </variant>
      </variantList>
    </layout>
    <layout>
      <configItem>
        <name>ml</name>
        
        <shortDescription>bm</shortDescription>
        <description>Bambara</description>
        <languageList>
          <iso639Id>bam</iso639Id>
        </languageList>
      </configItem>
      <variantList>
        <variant>
          <configItem>
            <name>fr-oss</name>
            
            <shortDescription>fr</shortDescription>
            <description>French (Mali, alternative)</description>
            <languageList>
              <iso639Id>fra</iso639Id>
            </languageList>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>us-mac</name>
            
            <shortDescription>en</shortDescription>
            <description>English (Mali, US Macintosh)</description>
            <languageList>
              <iso639Id>eng</iso639Id>
            </languageList>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>us-intl</name>
            
            <shortDescription>en</shortDescription>
            <description>English (Mali, US international)</description>
            <languageList>
              <iso639Id>eng</iso639Id>
            </languageList>
          </configItem>
        </variant>
      </variantList>
    </layout>
    <layout>
      <configItem>
        <name>tz</name>
        
        <shortDescription>sw</shortDescription>
        <description>Swahili (Tanzania)</description>
        <languageList>
          <iso639Id>swa</iso639Id>
        </languageList>
      </configItem>
    </layout>
    <layout>
      <configItem>
        <name>ke</name>
        
        <shortDescription>sw</shortDescription>
        <description>Swahili (Kenya)</description>
        <languageList>
          <iso639Id>swa</iso639Id>
        </languageList>
      </configItem>
      <variantList>
        <variant>
          <configItem>
            <name>kik</name>
            
            <shortDescription>ki</shortDescription>
            <description>Kikuyu</description>
            <languageList>
              <iso639Id>kik</iso639Id>
            </languageList>
          </configItem>
        </variant>
      </variantList>
    </layout>
    <layout>
      <configItem>
        <name>bw</name>
        
        <shortDescription>tn</shortDescription>
        <description>Tswana</description>
        <languageList>
          <iso639Id>tsn</iso639Id>
        </languageList>
      </configItem>
    </layout>
    <layout>
      <configItem>
        <name>ph</name>
        
        <shortDescription>ph</shortDescription>
        <description>Filipino</description>
        <languageList>
          <iso639Id>eng</iso639Id>
          <iso639Id>bik</iso639Id>
          <iso639Id>ceb</iso639Id>
          <iso639Id>fil</iso639Id>
          <iso639Id>hil</iso639Id>
          <iso639Id>ilo</iso639Id>
          <iso639Id>pam</iso639Id>
          <iso639Id>pag</iso639Id>
          <iso639Id>phi</iso639Id>
          <iso639Id>tgl</iso639Id>
          <iso639Id>war</iso639Id>
        </languageList>
      </configItem>
      <variantList>
        <variant>
          <configItem>
            <name>qwerty-bay</name>
            <description>Filipino (QWERTY Baybayin)</description>
            <languageList>
              <iso639Id>bik</iso639Id>
              <iso639Id>ceb</iso639Id>
              <iso639Id>fil</iso639Id>
              <iso639Id>hil</iso639Id>
              <iso639Id>ilo</iso639Id>
              <iso639Id>pam</iso639Id>
              <iso639Id>pag</iso639Id>
              <iso639Id>phi</iso639Id>
              <iso639Id>tgl</iso639Id>
              <iso639Id>war</iso639Id>
            </languageList>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>capewell-dvorak</name>
            <description>Filipino (Capewell-Dvorak Latin)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>capewell-dvorak-bay</name>
            <description>Filipino (Capewell-Dvorak Baybayin)</description>
            <languageList>
              <iso639Id>bik</iso639Id>
              <iso639Id>ceb</iso639Id>
              <iso639Id>fil</iso639Id>
              <iso639Id>hil</iso639Id>
              <iso639Id>ilo</iso639Id>
              <iso639Id>pam</iso639Id>
              <iso639Id>pag</iso639Id>
              <iso639Id>phi</iso639Id>
              <iso639Id>tgl</iso639Id>
              <iso639Id>war</iso639Id>
            </languageList>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>capewell-qwerf2k6</name>
            <description>Filipino (Capewell-QWERF 2006 Latin)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>capewell-qwerf2k6-bay</name>
            <description>Filipino (Capewell-QWERF 2006 Baybayin)</description>
            <languageList>
              <iso639Id>bik</iso639Id>
              <iso639Id>ceb</iso639Id>
              <iso639Id>fil</iso639Id>
              <iso639Id>hil</iso639Id>
              <iso639Id>ilo</iso639Id>
              <iso639Id>pam</iso639Id>
              <iso639Id>pag</iso639Id>
              <iso639Id>phi</iso639Id>
              <iso639Id>tgl</iso639Id>
              <iso639Id>war</iso639Id>
            </languageList>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>colemak</name>
            <description>Filipino (Colemak Latin)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>colemak-bay</name>
            <description>Filipino (Colemak Baybayin)</description>
            <languageList>
              <iso639Id>bik</iso639Id>
              <iso639Id>ceb</iso639Id>
              <iso639Id>fil</iso639Id>
              <iso639Id>hil</iso639Id>
              <iso639Id>ilo</iso639Id>
              <iso639Id>pam</iso639Id>
              <iso639Id>pag</iso639Id>
              <iso639Id>phi</iso639Id>
              <iso639Id>tgl</iso639Id>
              <iso639Id>war</iso639Id>
            </languageList>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>dvorak</name>
            <description>Filipino (Dvorak Latin)</description>
          </configItem>
        </variant>
        <variant>
          <configItem>
            <name>dvorak-bay</name>
            <description>Filipino (Dvorak Baybayin)</description>
            <languageList>
              <iso639Id>bik</iso639Id>
              <iso639Id>ceb</iso639Id>
              <iso639Id>fil</iso639Id>
              <iso639Id>hil</iso639Id>
              <iso639Id>ilo</iso639Id>
              <iso639Id>pam</iso639Id>
              <iso639Id>pag</iso639Id>
              <iso639Id>phi</iso639Id>
              <iso639Id>tgl</iso639Id>
              <iso639Id>war</iso639Id>
            </languageList>
          </configItem>
        </variant>
      </variantList>
    </layout>
    <layout>
      <configItem>
        <name>md</name>
        <shortDescription>md</shortDescription>
        <description>Moldavian</description>
        <languageList>
          <iso639Id>rum</iso639Id>
        </languageList>
      </configItem>
      <variantList>
        <variant>
          <configItem>
            <name>gag</name>
            <shortDescription>gag</shortDescription>
            <description>Moldavian (Gagauz)</description>
            <languageList>
              <iso639Id>gag</iso639Id>
            </languageList>
          </configItem>
        </variant>
      </variantList>
    </layout>
  </layoutList>
  <optionList>
    <group allowMultipleSelection="true">
      
      <configItem>
        <name>grp</name>
        <description>Switching to another layout</description>
      </configItem>
      <option>
        <configItem>
          <name>grp:switch</name>
          <description>Right Alt (while pressed)</description>
        </configItem>
      </option>
      <option>
        <configItem>
          <name>grp:lswitch</name>
          <description>Left Alt (while pressed)</description>
        </configItem>
      </option>
      <option>
        <configItem>
          <name>grp:lwin_switch</name>
          <description>Left Win (while pressed)</description>
        </configItem>
      </option>
      <option>
        <configItem>
          <name>grp:rwin_switch</name>
          <description>Right Win (while pressed)</description>
        </configItem>
      </option>
      <option>
        <configItem>
          <name>grp:win_switch</name>
          <description>Any Win key (while pressed)</description>
        </configItem>
      </option>
      <option>
        <configItem>
          <name>grp:caps_switch</name>
          <description>Caps Lock (while pressed), Alt+Caps Lock does the original capslock action</description>
        </configItem>
      </option>
      <option>
        <configItem>
          <name>grp:rctrl_switch</name>
          <description>Right Ctrl (while pressed)</description>
        </configItem>
      </option>
      <option>
        <configItem>
          <name>grp:toggle</name>
          <description>Right Alt</description>
        </configItem>
      </option>
      <option>
        <configItem>
          <name>grp:lalt_toggle</name>
          <description>Left Alt</description>
        </configItem>
      </option>
      <option>
        <configItem>
          <name>grp:caps_toggle</name>
          <description>Caps Lock</description>
        </configItem>
      </option>
      <option>
        <configItem>
          <name>grp:shift_caps_toggle</name>
          <description>Shift+Caps Lock</description>
        </configItem>
      </option>
      <option>
        <configItem>
          <name>grp:shift_caps_switch</name>
          <description>Caps Lock (to first layout), Shift+Caps Lock (to last layout)</description>
        </configItem>
      </option>
      <option>
        <configItem>
          <name>grp:win_menu_switch</name>
          <description>Left Win (to first layout), Right Win/Menu (to last layout)</description>
        </configItem>
      </option>
      <option>
        <configItem>
          <name>grp:lctrl_rctrl_switch</name>
          <description>Left Ctrl (to first layout), Right Ctrl (to last layout)</description>
        </configItem>
      </option>
      <option>
        <configItem>
          <name>grp:alt_caps_toggle</name>
          <description>Alt+Caps Lock</description>
        </configItem>
      </option>
      <option>
        <configItem>
          <name>grp:shifts_toggle</name>
          <description>Both Shift keys together</description>
        </configItem>
      </option>
      <option>
        <configItem>
          <name>grp:alts_toggle</name>
          <description>Both Alt keys together</description>
        </configItem>
      </option>
      <option>
        <configItem>
          <name>grp:ctrls_toggle</name>
          <description>Both Ctrl keys together</description>
        </configItem>
      </option>
      <option>
        <configItem>
          <name>grp:ctrl_shift_toggle</name>
          <description>Ctrl+Shift</description>
        </configItem>
      </option>
      <option>
        <configItem>
          <name>grp:lctrl_lshift_toggle</name>
          <description>Left Ctrl+Left Shift</description>
        </configItem>
      </option>
      <option>
        <configItem>
          <name>grp:rctrl_rshift_toggle</name>
          <description>Right Ctrl+Right Shift</description>
        </configItem>
      </option>
      <option>
        <configItem>
          <name>grp:ctrl_alt_toggle</name>
          <description>Alt+Ctrl</description>
        </configItem>
      </option>
      <option>
        <configItem>
          <name>grp:alt_shift_toggle</name>
          <description>Alt+Shift</description>
        </configItem>
      </option>
      <option>
        <configItem>
          <name>grp:lalt_lshift_toggle</name>
          <description>Left Alt+Left Shift</description>
        </configItem>
      </option>
      <option>
        <configItem>
          <name>grp:alt_space_toggle</name>
          <description>Alt+Space</description>
        </configItem>
      </option>
      <option>
        <configItem>
          <name>grp:menu_toggle</name>
          <description>Menu</description>
        </configItem>
      </option>
      <option>
        <configItem>
          <name>grp:lwin_toggle</name>
          <description>Left Win</description>
        </configItem>
      </option>
      <option>
        <configItem>
          <name>grp:win_space_toggle</name>
          <description>Win Key+Space</description>
        </configItem>
      </option>
      <option>
        <configItem>
          <name>grp:rwin_toggle</name>
          <description>Right Win</description>
        </configItem>
      </option>
      <option>
        <configItem>
          <name>grp:lshift_toggle</name>
          <description>Left Shift</description>
        </configItem>
      </option>
      <option>
        <configItem>
          <name>grp:rshift_toggle</name>
          <description>Right Shift</description>
        </configItem>
      </option>
      <option>
        <configItem>
          <name>grp:lctrl_toggle</name>
          <description>Left Ctrl</description>
        </configItem>
      </option>
      <option>
        <configItem>
          <name>grp:rctrl_toggle</name>
          <description>Right Ctrl</description>
        </configItem>
      </option>
      <option>
        <configItem>
          <name>grp:sclk_toggle</name>
          <description>Scroll Lock</description>
        </configItem>
      </option>
      <option>
        <configItem>
          <name>grp:lctrl_lwin_rctrl_menu</name>
          <description>LeftCtrl+LeftWin (to first layout), RightCtrl+Menu (to second layout)</description>
        </configItem>
      </option>
    </group>
    <group allowMultipleSelection="true">
      
      <configItem>
        <name>lv3</name>
        <description>Key to choose 3rd level</description>
      </configItem>
      <option>
        <configItem>
          <name>lv3:switch</name>
          <description>Right Ctrl</description>
        </configItem>
      </option>
      <option>
        <configItem>
          <name>lv3:menu_switch</name>
          <description>Menu</description>
        </configItem>
      </option>
      <option>
        <configItem>
          <name>lv3:win_switch</name>
          <description>Any Win key</description>
        </configItem>
      </option>
      <option>
        <configItem>
          <name>lv3:lwin_switch</name>
          <description>Left Win</description>
        </configItem>
      </option>
      <option>
        <configItem>
          <name>lv3:rwin_switch</name>
          <description>Right Win</description>
        </configItem>
      </option>
      <option>
        <configItem>
          <name>lv3:alt_switch</name>
          <description>Any Alt key</description>
        </configItem>
      </option>
      <option>
        <configItem>
          <name>lv3:lalt_switch</name>
          <description>Left Alt</description>
        </configItem>
      </option>
      <option>
        <configItem>
          <name>lv3:ralt_switch</name>
          <description>Right Alt</description>
        </configItem>
      </option>
      <option>
        <configItem>
          <name>lv3:ralt_switch_multikey</name>
          <description>Right Alt, Shift+Right Alt key is Compose</description>
        </configItem>
      </option>
      <option>
        <configItem>
          <name>lv3:ralt_alt</name>
          <description>Right Alt key never chooses 3rd level</description>
        </configItem>
      </option>
      <option>
        <configItem>
          <name>lv3:enter_switch</name>
          <description>Enter on keypad</description>
        </configItem>
      </option>
      <option>
        <configItem>
          <name>lv3:caps_switch</name>
          <description>Caps Lock</description>
        </configItem>
      </option>
      <option>
        <configItem>
          <name>lv3:bksl_switch</name>
          <description>Backslash</description>
        </configItem>
      </option>
      <option>
        <configItem>
          <name>lv3:lsgt_switch</name>
          <description>&lt;Less/Greater&gt;</description>
        </configItem>
      </option>
      <option>
        <configItem>
          <name>lv3:caps_switch_latch</name>
          <description>Caps Lock chooses 3rd level, acts as onetime lock when pressed together with another 3rd-level-chooser</description>
        </configItem>
      </option>
      <option>
        <configItem>
          <name>lv3:bksl_switch_latch</name>
          <description>Backslash chooses 3rd level, acts as onetime lock when pressed together with another 3rd-level-chooser</description>
        </configItem>
      </option>
      <option>
        <configItem>
          <name>lv3:lsgt_switch_latch</name>
          <description>&lt;Less/Greater&gt; chooses 3rd level, acts as onetime lock when pressed together with another 3rd-level-chooser</description>
        </configItem>
      </option>
    </group>
    <group allowMultipleSelection="true">
      
      <configItem>
        <name>ctrl</name>
        <description>Ctrl key position</description>
      </configItem>
      <option>
        <configItem>
          <name>ctrl:nocaps</name>
          <description>Caps Lock as Ctrl</description>
        </configItem>
      </option>
      <option>
        <configItem>
          <name>ctrl:lctrl_meta</name>
          <description>Left Ctrl as Meta</description>
        </configItem>
      </option>
      <option>
        <configItem>
          <name>ctrl:swapcaps</name>
          <description>Swap Ctrl and Caps Lock</description>
        </configItem>
      </option>
      <option>
        <configItem>
          <name>ctrl:ac_ctrl</name>
          <description>At left of 'A'</description>
        </configItem>
      </option>
      <option>
        <configItem>
          <name>ctrl:aa_ctrl</name>
          <description>At bottom left</description>
        </configItem>
      </option>
      <option>
        <configItem>
          <name>ctrl:rctrl_ralt</name>
          <description>Right Ctrl as Right Alt</description>
        </configItem>
      </option>
      <option>
        <configItem>
          <name>ctrl:menu_rctrl</name>
          <description>Menu as Right Ctrl</description>
        </configItem>
      </option>
      <option>
        <configItem>
          <name>ctrl:ctrl_ralt</name>
          <description>Right Alt as Right Ctrl</description>
        </configItem>
      </option>
      <option>
        <configItem>
          <name>ctrl:swap_lalt_lctl</name>
          <description>Swap Left Alt key with Left Ctrl key</description>
        </configItem>
      </option>
      <option>
        <configItem>
          <name>ctrl:swap_lwin_lctl</name>
          <description>Swap Left Win key with Left Ctrl key</description>
        </configItem>
      </option><option>
        <configItem>
          <name>ctrl:swap_rwin_rctl</name>
          <description>Swap Right Win key with Right Ctrl key</description>
        </configItem>
      </option>
      <option>
        <configItem>
          <name>ctrl:swap_lalt_lctl_lwin</name>
          <description>Left Alt as Ctrl, Left Ctrl as Win, Left Win as Alt</description>
        </configItem>
      </option>
    </group>
    <group allowMultipleSelection="true">
      
      <configItem>
        <name>grp_led</name>
        <description>Use keyboard LED to show alternative layout</description>
      </configItem>
      <option>
        <configItem>
          <name>grp_led:num</name>
          <description>Num Lock</description>
        </configItem>
      </option>
      <option>
        <configItem>
          <name>grp_led:caps</name>
          <description>Caps Lock</description>
        </configItem>
      </option>
      <option>
        <configItem>
          <name>grp_led:scroll</name>
          <description>Scroll Lock</description>
        </configItem>
      </option>
    </group>
    <group allowMultipleSelection="false">
      
      <configItem>
        <name>keypad</name>
        <description>Layout of numeric keypad</description>
      </configItem>
      <option>
        <configItem>
          <name>keypad:legacy</name>
          <description>Legacy</description>
       </configItem>
      </option>
      <option>
        <configItem>
          <name>keypad:oss</name>
          <description>Unicode additions (arrows and math operators)</description>
       </configItem>
      </option>
      <option>
        <configItem>
          <name>keypad:future</name>
          <description>Unicode additions (arrows and math operators; math operators on default level)</description>
       </configItem>
      </option>
      <option>
        <configItem>
          <name>keypad:legacy_wang</name>
          <description>Legacy Wang 724</description>
       </configItem>
      </option>
      <option>
        <configItem>
          <name>keypad:oss_wang</name>
          <description>Wang 724 keypad with Unicode additions (arrows and math operators)</description>
       </configItem>
      </option>
      <option>
        <configItem>
          <name>keypad:future_wang</name>
          <description>Wang 724 keypad with Unicode additions (arrows and math operators; math operators on default level)</description>
       </configItem>
      </option>
      <option>
        <configItem>
          <name>keypad:hex</name>
          <description>Hexadecimal</description>
       </configItem>
      </option>
      <option>
        <configItem>
          <name>keypad:atm</name>
          <description>ATM/phone-style</description>
       </configItem>
      </option>
    </group>
    
    <group allowMultipleSelection="false">
      
      <configItem>
        <name>kpdl</name>
        <description>Numeric keypad delete key behaviour</description>
      </configItem>
      <option>
        <configItem>
          
          <name>kpdl:dot</name>
          <description>Legacy key with dot</description>
       </configItem>
      </option>
      <option>
        <configItem>
          <name>kpdl:comma</name>
          
          <description>Legacy key with comma</description>
       </configItem>
      </option>
      <option>
        <configItem>
          <name>kpdl:dotoss</name>
          <description>Four-level key with dot</description>
       </configItem>
      </option>
      <option>
        <configItem>
          <name>kpdl:dotoss_latin9</name>
          <description>Four-level key with dot, Latin-9 only</description>
       </configItem>
      </option>
      <option>
        <configItem>
          <name>kpdl:commaoss</name>
          <description>Four-level key with comma</description>
       </configItem>
      </option>
      <option>
        <configItem>
          <name>kpdl:momayyezoss</name>
          <description>Four-level key with momayyez</description>
       </configItem>
      </option>
      <option>
        <configItem>
          <name>kpdl:kposs</name>
          
          <description>Four-level key with abstract separators</description>
       </configItem>
      </option>
      <option>
        <configItem>
          <name>kpdl:semi</name>
          <description>Semicolon on third level</description>
       </configItem>
      </option>
    </group>
    <group allowMultipleSelection="false">
      
      <configItem>
        <name>caps</name>
        <description>Caps Lock key behavior</description>
      </configItem>
      <option>
        <configItem>
          <name>caps:internal</name>
          <description>Caps Lock uses internal capitalization; Shift "pauses" Caps Lock</description>
        </configItem>
      </option>
      <option>
        <configItem>
          <name>caps:internal_nocancel</name>
          <description>Caps Lock uses internal capitalization; Shift doesn't affect Caps Lock</description>
        </configItem>
      </option>
      <option>
        <configItem>
          <name>caps:shift</name>
          <description>Caps Lock acts as Shift with locking; Shift "pauses" Caps Lock</description>
        </configItem>
      </option>
      <option>
        <configItem>
          <name>caps:shift_nocancel</name>
          <description>Caps Lock acts as Shift with locking; Shift doesn't affect Caps Lock</description>
        </configItem>
      </option>
      <option>
        <configItem>
          <name>caps:capslock</name>
          <description>Caps Lock toggles normal capitalization of alphabetic characters</description>
        </configItem>
      </option>
      <option>
        <configItem>
          <name>caps:numlock</name>
          <description>Make Caps Lock an additional Num Lock</description>
        </configItem>
      </option>
      <option>
        <configItem>
          <name>caps:swapescape</name>
          <description>Swap ESC and Caps Lock</description>
        </configItem>
      </option>
      <option>
        <configItem>
          <name>caps:escape</name>
          <description>Make Caps Lock an additional ESC</description>
        </configItem>
      </option>
      <option>
        <configItem>
          <name>caps:backspace</name>
          <description>Make Caps Lock an additional Backspace</description>
        </configItem>
      </option>
      <option>
        <configItem>
          <name>caps:super</name>
          <description>Make Caps Lock an additional Super</description>
        </configItem>
      </option>
      <option>
        <configItem>
          <name>caps:hyper</name>
          <description>Make Caps Lock an additional Hyper</description>
        </configItem>
      </option>
      <option>
        <configItem>
          <name>caps:shiftlock</name>
          <description>Caps Lock toggles ShiftLock (affects all keys)</description>
        </configItem>
      </option>
      <option>
        <configItem>
          <name>caps:none</name>
          <description>Caps Lock is disabled</description>
        </configItem>
      </option>
      <option>
        <configItem>
          <name>caps:ctrl_modifier</name>
          <description>Make Caps Lock an additional Ctrl</description>
        </configItem>
      </option>
    </group>
    <group allowMultipleSelection="false">
      
      <configItem>
        <name>altwin</name>
        <description>Alt/Win key behavior</description>
      </configItem>
      <option>
        <configItem>
          <name>altwin:menu</name>
          <description>Add the standard behavior to Menu key</description>
        </configItem>
      </option>
      <option>
        <configItem>
          <name>altwin:meta_alt</name>
          <description>Alt and Meta are on Alt keys</description>
        </configItem>
      </option>
      <option>
        <configItem>
          <name>altwin:alt_win</name>
          <description>Alt is mapped to Win keys (and the usual Alt keys)</description>
        </configItem>
      </option>
      <option>
        <configItem>
          <name>altwin:ctrl_win</name>
          <description>Ctrl is mapped to Win keys (and the usual Ctrl keys)</description>
        </configItem>
      </option>
      <option>
        <configItem>
          <name>altwin:ctrl_alt_win</name>
          <description>Ctrl is mapped to Alt keys, Alt is mapped to Win keys</description>
        </configItem>
      </option>
      <option>
        <configItem>
          <name>altwin:meta_win</name>
          <description>Meta is mapped to Win keys</description>
        </configItem>
      </option>
      <option>
        <configItem>
          <name>altwin:left_meta_win</name>
          <description>Meta is mapped to Left Win</description>
        </configItem>
      </option>
      <option>
        <configItem>
          <name>altwin:hyper_win</name>
          <description>Hyper is mapped to Win keys</description>
        </configItem>
      </option>
      <option>
        <configItem>
          <name>altwin:alt_super_win</name>
          <description>Alt is mapped to Right Win, Super to Menu</description>
        </configItem>
      </option>
      <option>
        <configItem>
          <name>altwin:swap_lalt_lwin</name>
          <description>Left Alt is swapped with Left Win</description>
        </configItem>
      </option>
      <option>
        <configItem>
          <name>altwin:swap_alt_win</name>
          <description>Alt is swapped with Win</description>
        </configItem>
      </option>
    </group>
    <group allowMultipleSelection="true">
      
      <configItem>
        <name>Compose key</name>
        <description>Position of Compose key</description>
      </configItem>
      <option>
        <configItem>
          <name>compose:ralt</name>
          <description>Right Alt</description>
        </configItem>
      </option>
      <option>
        <configItem>
          <name>compose:lwin</name>
          <description>Left Win</description>
        </configItem>
      </option>
      <option>
        <configItem>
          <name>compose:lwin-altgr</name>
          <description>3rd level of Left Win</description>
        </configItem>
      </option>
      <option>
        <configItem>
          <name>compose:rwin</name>
          <description>Right Win</description>
        </configItem>
      </option>
      <option>
        <configItem>
          <name>compose:rwin-altgr</name>
          <description>3rd level of Right Win</description>
        </configItem>
      </option>
      <option>
        <configItem>
          <name>compose:menu</name>
          <description>Menu</description>
        </configItem>
      </option>
      <option>
        <configItem>
          <name>compose:menu-altgr</name>
          <description>3rd level of Menu</description>
        </configItem>
      </option>
      <option>
        <configItem>
          <name>compose:lctrl</name>
          <description>Left Ctrl</description>
        </configItem>
      </option>
      <option>
        <configItem>
          <name>compose:lctrl-altgr</name>
          <description>3rd level of Left Ctrl</description>
        </configItem>
      </option>
      <option>
        <configItem>
          <name>compose:rctrl</name>
          <description>Right Ctrl</description>
        </configItem>
      </option>
      <option>
        <configItem>
          <name>compose:rctrl-altgr</name>
          <description>3rd level of Right Ctrl</description>
        </configItem>
      </option>
      <option>
        <configItem>
          <name>compose:caps</name>
          <description>Caps Lock</description>
        </configItem>
      </option>
      <option>
        <configItem>
          <name>compose:caps-altgr</name>
          <description>3rd level of Caps Lock</description>
        </configItem>
      </option>
      <option>
        <configItem>
          <name>compose:102</name>
          <description>&lt;Less/Greater&gt;</description>
        </configItem>
      </option>
      <option>
        <configItem>
          <name>compose:102-altgr</name>
          <description>3rd level of &lt;Less/Greater&gt;</description>
        </configItem>
      </option>
      <option>
        <configItem>
          <name>compose:paus</name>
          <description>Pause</description>
        </configItem>
      </option>
      <option>
        <configItem>
          <name>compose:prsc</name>
          <description>PrtSc</description>
        </configItem>
      </option>
      <option>
        <configItem>
          <name>compose:sclk</name>
          <description>Scroll Lock</description>
        </configItem>
      </option>
    </group>
    <group allowMultipleSelection="true">
      <configItem>
        <name>compat</name>
        <description>Miscellaneous compatibility options</description>
      </configItem>
      <option>
        <configItem>
          <name>numpad:pc</name>
          <description>Default numeric keypad keys</description>
        </configItem>
      </option>
      <option>
        <configItem>
          <name>numpad:mac</name>
          <description>Numeric keypad keys always enter digits (as in Mac OS)</description>
        </configItem>
      </option>
      <option>
        <configItem>
          <name>numpad:microsoft</name>
          <description>NumLock on: digits, Shift switches to arrow keys, Numlock off: always arrow keys (as in MS Windows)</description>
        </configItem>
      </option>
      <option>
        <configItem>
          <name>numpad:shift3</name>
          <description>Shift does not cancel Num Lock, chooses 3rd level instead</description>
        </configItem>
      </option>
      <option>
        <configItem>
          <name>srvrkeys:none</name>
          <description>Special keys (Ctrl+Alt+&lt;key&gt;) handled in a server</description>
        </configItem>
      </option>
      <option>
        <configItem>
          <name>apple:alupckeys</name>
          <description>Apple Aluminium Keyboard: emulate PC keys (Print, Scroll Lock, Pause, Num Lock)</description>
        </configItem>
      </option>
      <option>
        <configItem>
          <name>shift:breaks_caps</name>
          <description>Shift cancels Caps Lock</description>
        </configItem>
      </option>
      <option>
        <configItem>
          <name>misc:typo</name>
          <description>Enable extra typographic characters</description>
        </configItem>
      </option>
      <option>
        <configItem>
          <name>shift:both_capslock</name>
          <description>Both Shift keys together toggle Caps Lock</description>
        </configItem>
      </option>
      <option>
        <configItem>
          <name>shift:both_capslock_cancel</name>
          <description>Both Shift keys together activate Caps Lock, one Shift key deactivates</description>
        </configItem>
      </option>
      <option>
        <configItem>
          <name>shift:both_shiftlock</name>
          <description>Both Shift keys together toggle ShiftLock</description>
        </configItem>
      </option>
      <option>
        <configItem>
          <name>keypad:pointerkeys</name>
          <description>Shift + NumLock toggles PointerKeys</description>
        </configItem>
      </option>
      <option>
        <configItem>
          <name>grab:break_actions</name>
          <description>Allow breaking grabs with keyboard actions (warning: security risk)</description>
        </configItem>
      </option>
      <option>
        <configItem>
          <name>grab:debug</name>
          <description>Allow grab and window tree logging</description>
        </configItem>
      </option>
    </group>
    <group allowMultipleSelection="true">
      
      <configItem>
        <name>currencysign</name>
        <description>Adding currency signs to certain keys</description>
      </configItem>
      <option>
        <configItem>
          <name>eurosign:e</name>
          <description>Euro on E</description>
        </configItem>
      </option>
      <option>
        <configItem>
          <name>eurosign:2</name>
          <description>Euro on 2</description>
        </configItem>
      </option>
      <option>
        <configItem>
          <name>eurosign:4</name>
          <description>Euro on 4</description>
        </configItem>
      </option>
      <option>
        <configItem>
          <name>eurosign:5</name>
          <description>Euro on 5</description>
        </configItem>
      </option>
      <option>
        <configItem>
          <name>rupeesign:4</name>
          <description>Rupee on 4</description>
        </configItem>
      </option>
    </group>
    <group allowMultipleSelection="true">
      <configItem>
        <name>lv5</name>
        <description>Key to choose 5th level</description>
      </configItem>
      <option>
        <configItem>
          <name>lv5:lsgt_switch_lock</name>
          <description>&lt;Less/Greater&gt; chooses 5th level, locks when pressed together with another 5th-level-chooser</description>
        </configItem>
      </option>
      <option>
        <configItem>
          <name>lv5:ralt_switch_lock</name>
          <description>Right Alt chooses 5th level, locks when pressed together with another 5th-level-chooser</description>
        </configItem>
      </option>
      <option>
        <configItem>
          <name>lv5:lwin_switch_lock</name>
          <description>Left Win chooses 5th level, locks when pressed together with another 5th-level-chooser</description>
        </configItem>
      </option>
      <option>
        <configItem>
          <name>lv5:rwin_switch_lock</name>
          <description>Right Win chooses 5th level, locks when pressed together with another 5th-level-chooser</description>
        </configItem>
      </option>

    </group>
    <group allowMultipleSelection="false">
      
      <configItem>
        <name>nbsp</name>
        <description>Using space key to input non-breakable space character</description>
      </configItem>
      <option>
        <configItem>
          <name>nbsp:none</name>
          <description>Usual space at any level</description>
        </configItem>
      </option>
      <option>
        <configItem>
          <name>nbsp:level2</name>
          <description>Non-breakable space character at second level</description>
        </configItem>
      </option>
      <option>
        <configItem>
          <name>nbsp:level3</name>
          <description>Non-breakable space character at third level</description>
        </configItem>
      </option>
      <option>
        <configItem>
          <name>nbsp:level3s</name>
          <description>Non-breakable space character at third level, nothing at fourth level</description>
        </configItem>
      </option>
      <option>
        <configItem>
          <name>nbsp:level3n</name>
          <description>Non-breakable space character at third level, thin non-breakable space character at fourth level</description>
        </configItem>
      </option>
      <option>
        <configItem>
          <name>nbsp:level4</name>
          <description>Non-breakable space character at fourth level</description>
        </configItem>
      </option>
      <option>
        <configItem>
          <name>nbsp:level4n</name>
          <description>Non-breakable space character at fourth level, thin non-breakable space character at sixth level</description>
        </configItem>
      </option>
      <option>
        <configItem>
          <name>nbsp:level4nl</name>
          <description>Non-breakable space character at fourth level, thin non-breakable space character at sixth level (via Ctrl+Shift)</description>
        </configItem>
      </option>
      <option>
        <configItem>
          <name>nbsp:zwnj2</name>
          <description>Zero-width non-joiner character at second level</description>
        </configItem>
      </option>
      <option>
        <configItem>
          <name>nbsp:zwnj2zwj3</name>
          <description>Zero-width non-joiner character at second level, zero-width joiner character at third level</description>
        </configItem>
      </option>
      <option>
        <configItem>
          <name>nbsp:zwnj2zwj3nb4</name>
          <description>Zero-width non-joiner character at second level, zero-width joiner character at third level, non-breakable space character at fourth level</description>
        </configItem>
      </option>
      <option>
        <configItem>
          <name>nbsp:zwnj2nb3</name>
          <description>Zero-width non-joiner character at second level, non-breakable space character at third level</description>
        </configItem>
      </option>
      <option>
        <configItem>
          <name>nbsp:zwnj2nb3s</name>
          <description>Zero-width non-joiner character at second level, non-breakable space character at third level, nothing at fourth level</description>
        </configItem>
      </option>
      <option>
        <configItem>
          <name>nbsp:zwnj2nb3zwj4</name>
          <description>Zero-width non-joiner character at second level, non-breakable space character at third level, zero-width joiner at fourth level</description>
        </configItem>
      </option>
      <option>
        <configItem>
          <name>nbsp:zwnj2nb3nnb4</name>
          <description>Zero-width non-joiner character at second level, non-breakable space character at third level, thin non-breakable space at fourth level</description>
        </configItem>
      </option>
      <option>
        <configItem>
          <name>nbsp:zwnj3zwj4</name>
          <description>Zero-width non-joiner character at third level, zero-width joiner at fourth level</description>
        </configItem>
      </option>
    </group>
    <group allowMultipleSelection="true">
      <configItem>
        <name>japan</name>
        <description>Japanese keyboard options</description>
      </configItem>
      <option>
        <configItem>
          <name>japan:kana_lock</name>
          <description>Kana Lock key is locking</description>
       </configItem>
      </option>
      <option>
        <configItem>
          <name>japan:nicola_f_bs</name>
          <description>NICOLA-F style Backspace</description>
       </configItem>
      </option>
      <option>
        <configItem>
          <name>japan:hztg_escape</name>
          <description>Make Zenkaku Hankaku an additional ESC</description>
       </configItem>
      </option>
    </group>
    <group allowMultipleSelection="true">
      <configItem>
        <name>korean</name>
        <description>Korean Hangul/Hanja keys</description>
      </configItem>
      <option>
        <configItem>
          <name>korean:hw_keys</name>
          <description>Hardware Hangul/Hanja keys</description>
        </configItem>
      </option>
      <option>
        <configItem>
          <name>korean:ralt_rctrl</name>
          <description>Right Alt as Hangul, right Ctrl as Hanja</description>
        </configItem>
      </option>
      <option>
        <configItem>
          <name>korean:rctrl_ralt</name>
          <description>Right Ctrl as Hangul, right Alt as Hanja</description>
        </configItem>
      </option>
    </group>
    <group allowMultipleSelection="false">
      <configItem>
        <name>esperanto</name>
        <description>Adding Esperanto supersigned letters</description>
      </configItem>
      <option>
        <configItem>
          <name>esperanto:qwerty</name>
          <description>To the corresponding key in a Qwerty layout</description>
       </configItem>
      </option>
      <option>
        <configItem>
          <name>esperanto:dvorak</name>
          <description>To the corresponding key in a Dvorak layout</description>
       </configItem>
      </option>
      <option>
        <configItem>
          <name>esperanto:colemak</name>
          <description>To the corresponding key in a Colemak layout</description>
       </configItem>
      </option>
    </group>
    <group allowMultipleSelection="true">
      <configItem>
        <name>solaris</name>
        <description>Maintain key compatibility with old Solaris keycodes</description>
      </configItem>
      <option>
        <configItem>
          <name>solaris:sun_compat</name>
          <description>Sun Key compatibility</description>
        </configItem>
      </option>
    </group>
    <group allowMultipleSelection="true">
      <configItem>
        <name>terminate</name>
        <description>Key sequence to kill the X server</description>
      </configItem>
      <option>
        <configItem>
          <name>terminate:ctrl_alt_bksp</name>
          <description>Ctrl + Alt + Backspace</description>
        </configItem>
      </option>
    </group>
  </optionList>
</xkbConfigRegistry>