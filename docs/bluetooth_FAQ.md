收集遇到的 `bluetooth` 问题，使用的是 `deepin`。

---------------------------------------------

## 控制中心不显示蓝牙模块

蓝牙模块不显示时，可以按照以下步骤来查找原因：

1. 执行 `sudo dmesg|grub bluetooth` 查看输出结果
    
    * 输出里有 `timeout`

        此时再用 `systemctl status bluetooth.service` 看下服务有没有正常启动，错误信息是什么，然后根据错误去网上搜索答案。
    
        如果蓝牙是由网卡提供的，此时可以试着开关一下网卡的硬件开关。因为内核在初始化网卡之前初始化蓝牙，就会导致蓝牙设备不可用，重新初始化一次就好了。
        如果还是无效那就重启进入 `window`，再 `window` 里开关一次蓝牙再重启到 `linux`。
    
    * 输出里无 `timeout`
    
        这就表明初始化没问题，继续下一步

-------------------------------------------

2. 执行 `sudo hciconfig` 查看设备当前的状态
    
    * 状态为 `DOWN`
    
        则执行 `sudo hciconfig hci<number> up`，其中 **number** 为前面输出显示的。
        如果命令执行失败，参照第一步。
    
    * 状态为 `UP`
        
        继续下一步

-------------------------------------------

3. 执行 `sudo rfkill list` 查看设备是否被 `block`

    * 被 `block`
    
        则执行 `sudo rfkill <dev> unblock`
    
如果尝试完上述步骤，蓝牙仍不可用，那就反馈吧，提供日志及 `lsusb` 输出，等待问题被解决。

-------------------------------------------

## 蓝牙鼠标使用中频繁断开

蓝牙鼠标在使用过程中频繁出现了断开问题，试着从 `window` 那里获取最新的驱动，然后转成 `linux firmware`，方法请查看: [Firmware 安装](bluetooth_install-firmware.md)

如果经过尝试还是没有效果, 那就反馈吧.

-------------------------------------------

## 蓝牙音箱/耳机连上就断开

蓝牙音箱如果断开，先按照蓝牙耳机的方法试一下。如果不是，那就考虑是不是电脑的蓝牙不支持蓝牙高保真输出，因为 `deepin` 默认开启高保真。
试着安装 `pavucontrol` 将蓝牙的 `profile(配置)` 切换为其它的，不过这样会导致音效很差，建议换掉电脑的蓝牙设备吧。

若日志里看到 `a2dp-sink profile connect failed for XXX: Protocol not available` 之类的错误信息，可以试着修改 `/etc/pulse/daemon.conf` 文件，将 `exit-idle-time` 改为 `-1`。
