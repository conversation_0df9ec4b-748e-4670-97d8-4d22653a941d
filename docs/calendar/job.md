
# 任务 Job
 
日程表中的一个项目，称为任务 Job。在 JSON 中表示为一个对象。

它的字段：

## ID
数据类型 int

## 全天 AllDay

数据类型 bool


## 提醒 Remind

表示提醒的提前时间

数据类型: string

提醒和是否全天有关

- 不提醒 —— 空

全天时：
- 指定提前的天数和时间 —— 格式 "n;hh:mm", 比如 "1;08:00", 表示提醒时间为1天前的 08:00。

非全天时：
- 指定提前的分钟数 —— 格式 "n", 比如 "0" 表示不提前， 而”5" 表示提前5分钟提醒， "60" 表示提前 1 小时。

提醒的提前时间长度最长为 7 天。

## 重复规则 RRule
数据类型: string

重复规则表达式, 就是 RFC 5545 中规定的 RRule。

是用分号分隔的键值对,比如 `FREQ=DAILY;INTERVAL=3;COUNT=3`。

- FREQ:  事件重复频率，事件重复频率，有效值：DAILY(按天)，WEEKLY(按周)，MONTHLY(按月)，YEARLY(按年)

- INTERVAL: 事件重复的间隔，如按天重复时，INTERVAL=2，表示每2天重复一次，默认值为1。

- COUNT: 事件重复多少次后结束，该字段和 UNTIL 字段两者只能出现一个。

- UNTIL: 结束重复时间，格式如 20130102T170000Z，表示 2013-1-2 下午5点结束。

- BYDAY: 表示一周的某一天，有效值：MO(周一),TU(周二),WE(周三),TH(周四),FR(周五),SA(周六),SU(周日) ， 示例： BYDAY=MO,TH,SU 表示重复日期包括周一，周四，周日. 每个值前面可以用 ”+”, “-” 修饰,表示第几个和倒数第几个日子,如 BYDAY = 2MO 表示第2个星期一发生; BYDAY=MO,-1SU 表示每个星期一和最后一个星期日发生



- BYMONTHDAY: 表示一月的第几天发生,有效值是 [1 ~ 31] 和 [-31 ~ -1] ,如: BYMONTHDAY=2,18 表示一月的第2天,第18天发生; BYMONTHDAY=-1 表示一月的最后一天


- BYYEARDAY: 表示一年的第几天发生,有效值是 [1 ~ 366] 和 [-366 ~ -1], 如 BYYEARDAY=125 表示一年的第125年发生; BYYEARDAY=-1 表示一年的最后一天发生

- BYWEEKNO: 表示一年的第几周发生, 有效值是 [1 ~ 53] 和 [-53 ~ -1], 如 BYWEEKNO=2,23 表示一年的第2周,第23周发生

- BYMONTH: 表示一年中的第几个月发生, 有效值是 [1 ~ 12]

需要注意几点:

- 如果各字段所设置的值是无效的,如 BYMONTHDAY=30 ,则会忽略该值
- 如果某条事件的重复规则表达式缺少一些必要字段,如 YEARLY;BYMONTH=1 ,表示按年重复,每年的1月某日发生,现在缺少”日”字段,则从该事件的”开始日期”中获得


备注：重复为“每天”的规则是 `FREQ=DAILY`，重复为“每个工作日”的规则是 `FREQ=DAILY;BYDAY=MO,TU,WE,TH,FR`，重复为“每周”的规则是`FREQ=WEEKLY`，重复为“每月”的规则是`FREQ=MONTHLY`，重复为“每年”的规则是`FREQ=YEARLY`，如果 UI 上设置重复 N 次，则 COUNT=N+1。

备注：有关重复截至条件日期的设置，如果 job 开始时间为 2019-09-01 09:00，重复：每日，截至期为 2019-09-03，则需要设置 UNTIL=20190902T090000Z。
已知截至日期，求 UNTIL 值的计算方法是截至日期 - 1 天，然后时分秒设置为 job 的开始时间的时分秒。
已知 UNTIL 值，求截至日期的计算方法是 UNTIL 值中的日期 + 1 天。


## 标题 Title
数据类型: string

## 描述 Description
数据类型: string


## 类型 Type
数据类型：int

## 重复性ID RecurID
数据类型 int

数值等同于重复了几次才到这个 job。只读，根据重复规则计算出的。
重复性的 job 的本体的 RecurID 为 0，第一个复制体的 RecurID 为 1。


## 开始时间 Start
数据类型: string

格式为 RFC3339，比如"2006-01-02T15:04:05+07:00"。

## 结束时间 End
数据类型: string

格式为 RFC3339，比如"2006-01-02T15:04:05+07:00"。

## 忽略 Ignore
数据类型 []string


列表中的元素为要忽略的job的开始时间, 格式为 RFC3339，比如"2006-01-02T15:04:05+07:00"。

备注：界面如果修改了事件的开始时间，应该把这个值设置为空。

-----

对重复性 Job 的删除：有3种删除选项，分别是：删除所有活动，删除此活动和所有将来的活动，仅删除该活动。

- 删除所有活动，是把 id 对应的 job 删除，调用 DeleteJob 方法；

- 删除此活动和所有将来的活动，是修改 id 对应的 Job 的重复的截至时间，即 RRule 字段，增加 UNTIL 键值对，调用 UpdateJob 方法；

- 仅删除该活动，并不会真的删除这个 job，而是修改 id 对应的 Job 的 Ignore 字段，把要删除的 job 的开始时间设置到 Ignore 字段中；

对重复性 Job 的编辑： 有3种编辑选项，分别是：编辑所有活动，编辑此活动和所有将来的活动，仅编辑该活动。

以下为简略描述，如果不能理解参见对重复性 Job 的删除操作。

- 编辑所有活动，是直接修改 id 对应的 job 的相应字段；

- 编辑此活动和所有将来的活动，是新建一个 job，并且修改之前的 job 的截至日期为当天；

- 仅编辑此活动，是新建一个 job ，但是不能指定重复，必然是非重复的，强制为非重复的，然后设置旧 job 的 Ignore 字段；
