// Code generated by "dbusutil-gen em -type Manager,HuaweiDevice,Device"; DO NOT EDIT.

package fprintd1

import (
	"github.com/linuxdeepin/go-lib/dbusutil"
)

func (v *Device) GetExportedMethods() dbusutil.ExportedMethods {
	return dbusutil.ExportedMethods{
		{
			Name:   "Claim",
			Fn:     v.<PERSON>,
			InArgs: []string{"username"},
		},
		{
			Name:   "ClaimForce",
			Fn:     v.ClaimForce,
			InArgs: []string{"username"},
		},
		{
			Name:   "DeleteEnrolledFinger",
			Fn:     v.DeleteEnrolled<PERSON>inger,
			InArgs: []string{"username", "finger"},
		},
		{
			Name:   "DeleteEnrolledFingers",
			Fn:     v.DeleteEnrolledFingers,
			InArgs: []string{"username"},
		},
		{
			Name:   "EnrollStart",
			Fn:     v.EnrollStart,
			InArgs: []string{"finger"},
		},
		{
			Name: "EnrollStop",
			Fn:   v.Enroll<PERSON>top,
		},
		{
			Name:    "GetCapabilities",
			Fn:      v.GetCapabilities,
			OutArgs: []string{"caps"},
		},
		{
			Name:    "ListEnrolledFingers",
			Fn:      v.ListEnrolledFingers,
			InArgs:  []string{"username"},
			OutArgs: []string{"fingers"},
		},
		{
			Name: "Release",
			Fn:   v.Release,
		},
		{
			Name:   "VerifyStart",
			Fn:     v.VerifyStart,
			InArgs: []string{"finger"},
		},
		{
			Name: "VerifyStop",
			Fn:   v.VerifyStop,
		},
	}
}
func (v *HuaweiDevice) GetExportedMethods() dbusutil.ExportedMethods {
	return dbusutil.ExportedMethods{
		{
			Name:   "Claim",
			Fn:     v.Claim,
			InArgs: []string{"username"},
		},
		{
			Name:   "ClaimForce",
			Fn:     v.ClaimForce,
			InArgs: []string{"username"},
		},
		{
			Name:   "DeleteEnrolledFinger",
			Fn:     v.DeleteEnrolledFinger,
			InArgs: []string{"username", "finger"},
		},
		{
			Name:   "DeleteEnrolledFingers",
			Fn:     v.DeleteEnrolledFingers,
			InArgs: []string{"username"},
		},
		{
			Name:   "EnrollStart",
			Fn:     v.EnrollStart,
			InArgs: []string{"finger"},
		},
		{
			Name: "EnrollStop",
			Fn:   v.EnrollStop,
		},
		{
			Name:    "GetCapabilities",
			Fn:      v.GetCapabilities,
			OutArgs: []string{"caps"},
		},
		{
			Name:    "ListEnrolledFingers",
			Fn:      v.ListEnrolledFingers,
			InArgs:  []string{"username"},
			OutArgs: []string{"fingers"},
		},
		{
			Name: "Release",
			Fn:   v.Release,
		},
		{
			Name:   "VerifyStart",
			Fn:     v.VerifyStart,
			InArgs: []string{"finger"},
		},
		{
			Name: "VerifyStop",
			Fn:   v.VerifyStop,
		},
	}
}
func (v *Manager) GetExportedMethods() dbusutil.ExportedMethods {
	return dbusutil.ExportedMethods{
		{
			Name:    "GetDefaultDevice",
			Fn:      v.GetDefaultDevice,
			OutArgs: []string{"device"},
		},
		{
			Name:    "GetDevices",
			Fn:      v.GetDevices,
			OutArgs: []string{"devices"},
		},
		{
			Name: "PreAuthEnroll",
			Fn:   v.PreAuthEnroll,
		},
		{
			Name: "TriggerUDevEvent",
			Fn:   v.TriggerUDevEvent,
		},
	}
}
