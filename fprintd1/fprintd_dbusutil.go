// Code generated by "dbusutil-gen -type Manager -import github.com/godbus/dbus manager.go"; DO NOT EDIT.

package fprintd1

import (
	"github.com/godbus/dbus/v5"
)

func (v *Manager) setPropDevices(value []dbus.ObjectPath) {
	v.Devices = value
	v.emitPropChangedDevices(value)
}

func (v *Manager) emitPropChangedDevices(value []dbus.ObjectPath) error {
	return v.service.EmitPropertyChanged(v, "Devices", value)
}
