// Code generated by "dbusutil-gen em -type Authority,PAMTransaction,FPrintTransaction"; DO NOT EDIT.

package main

import (
	"github.com/linuxdeepin/go-lib/dbusutil"
)

func (v *Authority) GetExportedMethods() dbusutil.ExportedMethods {
	return dbusutil.ExportedMethods{
		{
			Name:   "CheckAuth",
			Fn:     v.<PERSON>uth,
			InArgs: []string{"details"},
		},
		{
			Name:    "CheckCookie",
			Fn:      v.CheckCookie,
			InArgs:  []string{"user", "cookie"},
			OutArgs: []string{"result", "authToken"},
		},
		{
			Name:    "HasCookie",
			Fn:      v.<PERSON>,
			InArgs:  []string{"user"},
			OutArgs: []string{"result"},
		},
		{
			Name:    "Start",
			Fn:      v.Start,
			InArgs:  []string{"authType", "user", "agent"},
			OutArgs: []string{"transaction"},
		},
	}
}
func (v *FPrintTransaction) GetExportedMethods() dbusutil.ExportedMethods {
	return dbusutil.ExportedMethods{
		{
			Name: "Authenticate",
			Fn:   v.Authenticate,
		},
		{
			Name: "End",
			Fn:   v.End,
		},
		{
			Name:   "SetUser",
			Fn:     v.SetUser,
			InArgs: []string{"user"},
		},
	}
}
func (v *PAMTransaction) GetExportedMethods() dbusutil.ExportedMethods {
	return dbusutil.ExportedMethods{
		{
			Name: "Authenticate",
			Fn:   v.Authenticate,
		},
		{
			Name: "End",
			Fn:   v.End,
		},
		{
			Name:   "SetUser",
			Fn:     v.SetUser,
			InArgs: []string{"user"},
		},
	}
}
