// Code generated by "dbusutil-gen em -type Manager"; DO NOT EDIT.

package main

import (
	"github.com/linuxdeepin/go-lib/dbusutil"
)

func (v *Manager) GetExportedMethods() dbusutil.ExportedMethods {
	return dbusutil.ExportedMethods{
		{
			Name:    "NewSearchWithStrDict",
			Fn:      v.NewSearchWithStrDict,
			InArgs:  []string{"dict"},
			OutArgs: []string{"md5sum", "ok"},
		},
		{
			Name:    "NewSearchWithStrList",
			Fn:      v.NewSearchWithStrList,
			InArgs:  []string{"list"},
			OutArgs: []string{"md5sum", "ok"},
		},
		{
			Name:    "SearchStartWithString",
			Fn:      v.SearchStartWithString,
			InArgs:  []string{"str", "md5sum"},
			OutArgs: []string{"result"},
		},
		{
			Name:    "SearchString",
			Fn:      v.SearchString,
			InArgs:  []string{"str", "md5sum"},
			OutArgs: []string{"result"},
		},
	}
}
