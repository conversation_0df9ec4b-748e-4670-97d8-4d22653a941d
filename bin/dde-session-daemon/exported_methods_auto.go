// Code generated by "dbusutil-gen em -type SessionDaemon"; DO NOT EDIT.

package main

import (
	"github.com/linuxdeepin/go-lib/dbusutil"
)

func (v *SessionDaemon) GetExportedMethods() dbusutil.ExportedMethods {
	return dbusutil.ExportedMethods{
		{
			Name:   "CallTrace",
			Fn:     v.CallTrace,
			InArgs: []string{"times", "seconds"},
		},
		{
			Name: "StartPart2",
			Fn:   v.StartPart2,
		},
	}
}
