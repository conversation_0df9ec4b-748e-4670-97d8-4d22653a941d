// Code generated by "dbusutil-gen em -type Manager"; DO NOT EDIT.

package ddcci

import (
	"github.com/linuxdeepin/go-lib/dbusutil"
)

func (v *Manager) GetExportedMethods() dbusutil.ExportedMethods {
	return dbusutil.ExportedMethods{
		{
			Name:    "CheckSupport",
			Fn:      v.CheckSupport,
			InArgs:  []string{"edidBase64"},
			OutArgs: []string{"outArg0"},
		},
		{
			Name:    "GetBrightness",
			Fn:      v.GetBrightness,
			InArgs:  []string{"edidBase64"},
			OutArgs: []string{"outArg0"},
		},
		{
			Name: "RefreshDisplays",
			Fn:   v.RefreshDisplays,
		},
		{
			Name:   "SetBrightness",
			Fn:     v.SetBrightness,
			InArgs: []string{"edidBase64", "value"},
		},
	}
}
