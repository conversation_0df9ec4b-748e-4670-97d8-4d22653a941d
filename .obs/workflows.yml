test_build:
  steps:
    - link_package:
        source_project: deepin:Develop:dde
        source_package: %{SCM_REPOSITORY_NAME}
        target_project: deepin:CI

    - configure_repositories:
        project: deepin:CI
        repositories:
          - name: deepin_develop
            paths:
              - target_project: deepin:CI
                target_repository: deepin_develop
            architectures:
              - x86_64
              - aarch64

          - name: debian
            paths:
              - target_project: deepin:CI
                target_repository: debian_sid
            architectures:
              - x86_64

  filters:
    event: pull_request

tag_build:
  steps:
    - trigger_services:
        project: deepin:Unstable:dde
        package: %{SCM_REPOSITORY_NAME}
  filters:
    event: tag_push

commit_build:
  steps:
    - trigger_services:
        project: deepin:Develop:dde
        package: %{SCM_REPOSITORY_NAME}
  filters:
    event: push
